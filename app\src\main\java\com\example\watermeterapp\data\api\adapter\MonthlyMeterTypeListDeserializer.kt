package com.example.watermeterapp.data.api.adapter

import com.example.watermeterapp.data.model.MonthlyMeterType
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonParseException
import java.lang.reflect.Type

/**
 * 月抄表类型列表的JSON反序列化器
 * 用于处理不同格式的月抄表类型列表数据
 */
class MonthlyMeterTypeListDeserializer : JsonDeserializer<List<MonthlyMeterType>> {
    
    override fun deserialize(
        json: JsonElement?, 
        typeOfT: Type?, 
        context: JsonDeserializationContext?
    ): List<MonthlyMeterType> {
        try {
            if (json == null) {
                return emptyList()
            }
            
            // 处理字符串响应（服务器可能返回纯文本）
            if (json.isJsonPrimitive && json.asJsonPrimitive.isString) {
                val responseStr = json.asString
                
                // 尝试解析字符串中的列表，假设格式为逗号分隔或其他分隔符
                val items = responseStr.split(",", ";", "\n")
                    .map { it.trim() }
                    .filter { it.isNotEmpty() }
                    .map { createMonthlyMeterType(it) }
                
                return items
            }
            
            // 处理JSON数组
            if (json.isJsonArray) {
                val result = mutableListOf<MonthlyMeterType>()
                val jsonArray = json.asJsonArray
                
                for (element in jsonArray) {
                    if (element.isJsonObject) {
                        val jsonObj = element.asJsonObject
                        
                        // 提取字段
                        val name = if (jsonObj.has("name")) jsonObj.get("name").asString else ""
                        val isSubmitted = if (jsonObj.has("isSubmitted")) jsonObj.get("isSubmitted").asBoolean else false
                        
                        // 检查是否有子表
                        val subMeters = if (jsonObj.has("subMeters") && jsonObj.get("subMeters").isJsonArray) {
                            val subArray = jsonObj.get("subMeters").asJsonArray
                            val subList = mutableListOf<MonthlyMeterType.SubMeterType>()
                            
                            for (subElement in subArray) {
                                if (subElement.isJsonObject) {
                                    val subObj = subElement.asJsonObject
                                    
                                    val subName = if (subObj.has("name")) subObj.get("name").asString else ""
                                    val subIsSubmitted = if (subObj.has("isSubmitted")) 
                                        subObj.get("isSubmitted").asBoolean else false
                                    val hasNoMeter = if (subObj.has("hasNoMeter"))
                                        subObj.get("hasNoMeter").asBoolean else false
                                    
                                    subList.add(MonthlyMeterType.SubMeterType(
                                        name = subName,
                                        isSubmitted = subIsSubmitted,
                                        hasNoMeter = hasNoMeter
                                    ))
                                }
                            }
                            
                            subList
                        } else {
                            emptyList()
                        }
                        
                        result.add(MonthlyMeterType(
                            parentType = name,
                            subTypes = subMeters,
                            expanded = false
                        ))
                    } else if (element.isJsonPrimitive && element.asJsonPrimitive.isString) {
                        // 如果数组元素是字符串，创建简单的MonthlyMeterType
                        result.add(createMonthlyMeterType(element.asString))
                    }
                }
                
                return result
            }
            
            // 无法识别的JSON格式
            return emptyList()
            
        } catch (e: Exception) {
            throw JsonParseException("解析月抄表类型列表失败", e)
        }
    }
    
    /**
     * 从字符串创建MonthlyMeterType对象
     */
    private fun createMonthlyMeterType(name: String): MonthlyMeterType {
        // 检查是否包含子表信息
        if (name.contains("/")) {
            val parts = name.split("/")
            val parentName = parts[0].trim()
            val subName = parts[1].trim()
            
            // 创建带有单个子表的MonthlyMeterType
            val subMeter = MonthlyMeterType.SubMeterType(
                name = subName,
                isSubmitted = false,
                hasNoMeter = false
            )
            
            return MonthlyMeterType(
                parentType = parentName,
                subTypes = listOf(subMeter),
                expanded = false
            )
        }
        
        // 创建不带子表的MonthlyMeterType
        return MonthlyMeterType(
            parentType = name,
            subTypes = emptyList(),
            expanded = false
        )
    }
} 