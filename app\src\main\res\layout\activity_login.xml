<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/welcome_background"
    tools:context=".ui.login.LoginActivity">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:padding="16dp"
        android:paddingBottom="32dp"
        android:clipToPadding="false">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivLogo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="64dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_water_meter"
        android:tint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/login_title"
        android:textColor="@color/white"
        android:textSize="28sp"
        android:textStyle="bold"
        android:shadowColor="#80000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivLogo" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/tilPhoneNumber"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:hint="@string/phone_hint"
        app:boxStrokeColor="@color/white"
        app:hintTextColor="@color/white"
        android:textColorHint="@color/white"
        app:boxCornerRadiusBottomEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusTopStart="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etPhoneNumber"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="phone"
            android:textColor="@color/white"
            android:maxLength="11" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnLogin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:padding="12dp"
        android:text="@string/login_btn"
        android:textSize="16sp"
        app:cornerRadius="8dp"
        app:elevation="4dp"
        android:backgroundTint="@color/accent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tilPhoneNumber" />

    <!-- 服务器设置区域容器 -->
    <LinearLayout
        android:id="@+id/layoutServerConfig"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btnLogin">

        <TextView
            android:id="@+id/tvServerSettingsTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="服务器设置"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:shadowColor="#80000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />

        <!-- 第一行：IP地址输入 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilServerAddress"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="服务器IP地址"
                app:boxStrokeColor="@color/white"
                app:hintTextColor="@color/white"
                android:textColorHint="@color/white"
                app:boxCornerRadiusBottomEnd="8dp"
                app:boxCornerRadiusBottomStart="8dp"
                app:boxCornerRadiusTopEnd="8dp"
                app:boxCornerRadiusTopStart="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etServerAddress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textUri"
                    android:textColor="@color/white"
                    android:hint="如：*************" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/tilServerPort"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="100dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:hint="端口"
                app:boxStrokeColor="@color/white"
                app:hintTextColor="@color/white"
                android:textColorHint="@color/white"
                app:boxCornerRadiusBottomEnd="8dp"
                app:boxCornerRadiusBottomStart="8dp"
                app:boxCornerRadiusTopEnd="8dp"
                app:boxCornerRadiusTopStart="8dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etServerPort"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:textColor="@color/white"
                    android:text="3002"
                    android:hint="3002" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- 第二行：保存按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSaveServer"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="保存配置"
                android:textColor="@color/white"
                app:strokeColor="@color/white"
                app:cornerRadius="8dp" />

        </LinearLayout>

    </LinearLayout>

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</FrameLayout>
