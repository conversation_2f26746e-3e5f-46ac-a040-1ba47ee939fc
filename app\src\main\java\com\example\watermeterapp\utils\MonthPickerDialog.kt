package com.example.watermeterapp.utils

import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.Window
import android.widget.Button
import android.widget.NumberPicker
import android.widget.TextView
import com.example.watermeterapp.R
import java.util.*

class MonthPickerDialog(
    private val context: Context,
    private val onMonthSelected: (Int) -> Unit
) {
    
    private var dialog: Dialog? = null
    
    /**
     * 显示月份选择对话框
     * @param currentMonth 当前选中的月份 (1-12)
     * @param smartRecommendedMonth 智能推荐的月份 (1-12)
     */
    fun show(currentMonth: Int = getCurrentMonth(), smartRecommendedMonth: Int = getSmartRecommendedMonth()) {
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_month_picker, null)
        
        dialog = Dialog(context).apply {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            setContentView(dialogView)
            setCancelable(true)
            window?.setBackgroundDrawableResource(android.R.color.transparent)
        }
        
        // 设置月份数组
        val months = arrayOf(
            "1月", "2月", "3月", "4月", "5月", "6月",
            "7月", "8月", "9月", "10月", "11月", "12月"
        )
        
        // 初始化NumberPicker
        val numberPicker = dialogView.findViewById<NumberPicker>(R.id.npMonth)
        numberPicker.apply {
            minValue = 0
            maxValue = months.size - 1
            displayedValues = months
            wrapSelectorWheel = true
            value = currentMonth - 1 // NumberPicker使用0-based索引
        }
        
        // 设置提示文本
        val tvMonthHint = dialogView.findViewById<TextView>(R.id.tvMonthHint)
        tvMonthHint.text = "智能推荐：${smartRecommendedMonth}月"
        
        // 取消按钮
        val btnCancel = dialogView.findViewById<Button>(R.id.btnCancel)
        btnCancel.setOnClickListener {
            dialog?.dismiss()
        }
        
        // 确认按钮
        val btnConfirm = dialogView.findViewById<Button>(R.id.btnConfirm)
        btnConfirm.setOnClickListener {
            val selectedMonth = numberPicker.value + 1 // 转换为1-based
            onMonthSelected(selectedMonth)
            dialog?.dismiss()
        }
        
        dialog?.show()
    }
    
    /**
     * 获取当前月份
     */
    private fun getCurrentMonth(): Int {
        return Calendar.getInstance().get(Calendar.MONTH) + 1
    }
    
    /**
     * 获取智能推荐月份
     * 智能逻辑：27号之后推荐下个月，否则推荐当前月
     */
    private fun getSmartRecommendedMonth(): Int {
        val calendar = Calendar.getInstance()
        val currentDay = calendar.get(Calendar.DAY_OF_MONTH)
        val currentMonth = calendar.get(Calendar.MONTH) + 1
        
        return if (currentDay >= 27) {
            // 27号之后推荐下个月
            if (currentMonth == 12) 1 else currentMonth + 1
        } else {
            // 27号之前推荐当前月
            currentMonth
        }
    }
    
    /**
     * 关闭对话框
     */
    fun dismiss() {
        dialog?.dismiss()
    }
}
