package com.example.watermeterapp.utils

import android.content.Context
import com.example.watermeterapp.data.model.User
import java.io.File
import org.json.JSONObject
import java.io.FileWriter
import java.io.FileReader
import java.io.BufferedReader

/**
 * 用户配置管理类
 * 用于保存和获取用户配置信息，通过文件方式持久化
 */
object UserConfig {
    
    private const val CONFIG_FILE_NAME = "user_config.json"
    
    /**
     * 保存用户信息到配置文件
     * 
     * @param context 应用上下文
     * @param user 用户对象
     * @return 是否保存成功
     */
    fun saveUserInfo(context: Context, user: User): <PERSON><PERSON>an {
        try {
            val configFile = File(context.filesDir, CONFIG_FILE_NAME)
            val userJson = JSONObject().apply {
                put("phoneNumber", user.phoneNumber ?: "")
                put("name", user.name ?: "")
                put("department", user.department ?: "")
                put("role", user.role ?: "")
                put("isAuthorized", user.isAuthorized)
                put("authLevel", user.authLevel ?: 1)
                // 注意：出于安全考虑，我们不保存token到这个文件中
            }
            
            FileWriter(configFile).use { writer ->
                writer.write(userJson.toString())
            }
            return true
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
    }
    
    /**
     * 从配置文件读取用户信息
     * 
     * @param context 应用上下文
     * @return 用户对象，如果读取失败则返回null
     */
    fun getUserInfo(context: Context): User? {
        try {
            val configFile = File(context.filesDir, CONFIG_FILE_NAME)
            if (!configFile.exists()) {
                return null
            }
            
            val jsonString = BufferedReader(FileReader(configFile)).use { reader ->
                reader.readText()
            }
            
            val jsonObject = JSONObject(jsonString)
            return User(
                phoneNumber = jsonObject.optString("phoneNumber"),
                name = jsonObject.optString("name"),
                department = jsonObject.optString("department"),
                role = jsonObject.optString("role"),
                isAuthorized = jsonObject.optBoolean("isAuthorized", false),
                authLevel = jsonObject.optInt("authLevel", 1),
                token = null // 从文件中不读取token
            )
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }
    
    /**
     * 删除用户配置文件
     * 
     * @param context 应用上下文
     * @return 是否删除成功
     */
    fun clearUserConfig(context: Context): Boolean {
        try {
            val configFile = File(context.filesDir, CONFIG_FILE_NAME)
            if (configFile.exists()) {
                return configFile.delete()
            }
            return true
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
    }
    
    /**
     * 检查用户配置文件是否存在
     * 
     * @param context 应用上下文
     * @return 是否存在
     */
    fun hasUserConfig(context: Context): Boolean {
        val configFile = File(context.filesDir, CONFIG_FILE_NAME)
        return configFile.exists()
    }
}