# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -XX:+UseG1GC -XX:+DisableExplicitGC
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
android.enableJetifier=true

# 开启Gradle缓存
org.gradle.caching=true
# 暂时禁用配置按需配置以避免编译问题
org.gradle.configureondemand=false
# 开启Kotlin增量编译
kotlin.incremental=true
# 暂时禁用Kotlin并行任务以避免守护进程崩溃
kotlin.parallel.tasks.in.project=false
# Kotlin编译守护进程JVM参数 - 使用更保守的设置
kotlin.daemon.jvm.options=-Xmx4g,-XX:MaxMetaspaceSize=512m,-XX:+UseG1GC,-XX:+DisableExplicitGC
# 修复Kotlin编译守护进程崩溃问题
kotlin.daemon.shutdown.grace.period.ms=5000
# 禁用Kotlin编译守护进程回退
kotlin.compiler.execution.strategy=in-process