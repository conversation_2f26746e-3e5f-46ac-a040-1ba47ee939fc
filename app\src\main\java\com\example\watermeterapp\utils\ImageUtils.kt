package com.example.watermeterapp.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import androidx.core.content.FileProvider
import android.util.Base64
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

/**
 * 图像工具类，用于处理照片和水印
 */
object ImageUtils {
    
    /**
     * 创建照片文件
     */
    fun createImageFile(context: Context): File {
        // 创建图像文件名
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File.createTempFile(
            "JPEG_${timeStamp}_",
            ".jpg",
            storageDir
        )
    }

    /**
     * 创建带自定义名称的照片文件
     */
    fun createImageFileWithName(context: Context, fileName: String): File {
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        return File(storageDir, fileName)
    }
    
    /**
     * 获取照片的URI
     */
    fun getUriForFile(context: Context, file: File): Uri {
        return FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )
    }
    
    /**
     * 添加水印到照片
     */
    fun addWatermark(context: Context, imagePath: String): String {
        val originalBitmap = MediaStore.Images.Media.getBitmap(context.contentResolver, Uri.fromFile(File(imagePath)))

        // 创建可绘制的位图
        val watermarkedBitmap = originalBitmap.copy(Bitmap.Config.ARGB_8888, true)

        // 设置水印内容
        val canvas = Canvas(watermarkedBitmap)
        val paint = Paint().apply {
            color = Color.WHITE
            alpha = 180
            textSize = 50f
            isAntiAlias = true
        }

        // 添加日期时间水印
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val dateTime = dateFormat.format(Date())
        val watermarkText = "水表抄录系统 - $dateTime"

        // 水印位置（右下角）
        val x = (originalBitmap.width - paint.measureText(watermarkText) - 20).toFloat()
        val y = (originalBitmap.height - 50).toFloat()

        // 绘制水印
        canvas.drawText(watermarkText, x, y, paint)

        // 保存带水印的图片
        val watermarkedFile = File(imagePath.replace(".jpg", "_watermarked.jpg"))
        FileOutputStream(watermarkedFile).use { out ->
            watermarkedBitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
        }

        // 回收位图
        originalBitmap.recycle()
        watermarkedBitmap.recycle()

        return watermarkedFile.absolutePath
    }

    /**
     * 添加自定义水印到照片
     * @param context 上下文
     * @param imagePath 原始图片路径
     * @param watermarkTitle 水印标题（如"日抄表-中水机房水表"）
     * @return 处理后的图片路径
     */
    fun addCustomWatermark(context: Context, imagePath: String, watermarkTitle: String): String {
        val originalBitmap = MediaStore.Images.Media.getBitmap(context.contentResolver, Uri.fromFile(File(imagePath)))

        // 创建可绘制的位图
        val watermarkedBitmap = originalBitmap.copy(Bitmap.Config.ARGB_8888, true)

        // 设置水印内容
        val canvas = Canvas(watermarkedBitmap)
        val paint = Paint().apply {
            color = Color.YELLOW
            alpha = 220
            textSize = 60f
            isAntiAlias = true
            setShadowLayer(3f, 1f, 1f, Color.BLACK)
            style = Paint.Style.FILL_AND_STROKE
            strokeWidth = 1f
        }

        // 创建水印文字：标题 + 时间
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val dateTime = dateFormat.format(Date())
        val watermarkText = "$watermarkTitle $dateTime"

        // 水印位置（右下角）
        val x = (originalBitmap.width - paint.measureText(watermarkText) - 20).toFloat()
        val y = (originalBitmap.height - 50).toFloat()

        // 绘制水印
        canvas.drawText(watermarkText, x, y, paint)

        // 保存带水印的图片，覆盖原文件
        FileOutputStream(File(imagePath)).use { out ->
            watermarkedBitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
        }

        // 回收位图
        originalBitmap.recycle()
        watermarkedBitmap.recycle()

        return imagePath
    }
    
    /**
     * 删除图片文件
     */
    fun deleteImageFile(imagePath: String): Boolean {
        val file = File(imagePath)
        return if (file.exists()) {
            file.delete()
        } else {
            false
        }
    }
}
