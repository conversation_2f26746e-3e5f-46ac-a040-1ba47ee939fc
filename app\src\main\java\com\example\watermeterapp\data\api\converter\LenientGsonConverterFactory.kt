package com.example.watermeterapp.data.api.converter

import com.google.gson.Gson
import com.google.gson.TypeAdapter
import com.google.gson.reflect.TypeToken
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.ResponseBody
import retrofit2.Converter
import retrofit2.Retrofit
import java.lang.reflect.Type

/**
 * 自定义的Gson转换工厂，更宽松地处理JSON响应
 * 解决"JSON document was not fully consumed"错误
 */
class LenientGsonConverterFactory private constructor(private val gson: Gson) : Converter.Factory() {

    companion object {
        @JvmStatic
        fun create(gson: Gson): LenientGsonConverterFactory {
            return LenientGsonConverterFactory(gson)
        }
    }

    override fun responseBodyConverter(
        type: Type,
        annotations: Array<out Annotation>,
        retrofit: Retrofit
    ): Converter<ResponseBody, *> {
        val adapter = gson.getAdapter(TypeToken.get(type))
        return LenientGsonResponseBodyConverter(gson, adapter)
    }

    override fun requestBodyConverter(
        type: Type,
        parameterAnnotations: Array<out Annotation>,
        methodAnnotations: Array<out Annotation>,
        retrofit: Retrofit
    ): Converter<*, RequestBody> {
        val adapter = gson.getAdapter(TypeToken.get(type))
        return GsonRequestBodyConverter(gson, adapter)
    }
}

/**
 * 宽松的Gson响应体转换器
 */
internal class LenientGsonResponseBodyConverter<T>(
    private val gson: Gson,
    private val adapter: TypeAdapter<T>
) : Converter<ResponseBody, T> {

    override fun convert(value: ResponseBody): T {
        try {
            val jsonReader = gson.newJsonReader(value.charStream())
            jsonReader.isLenient = true
            val result = adapter.read(jsonReader)
            
            // 不再检查是否完全消费
            // 即使有剩余内容也不抛出异常
            
            return result
        } finally {
            value.close()
        }
    }
}

/**
 * Gson请求体转换器
 */
internal class GsonRequestBodyConverter<T>(
    private val gson: Gson,
    private val adapter: TypeAdapter<T>
) : Converter<T, RequestBody> {

    override fun convert(value: T): RequestBody {
        val jsonString = gson.toJson(value)
        val contentType = "application/json; charset=UTF-8".toMediaType()
        return jsonString.toRequestBody(contentType)
    }
} 