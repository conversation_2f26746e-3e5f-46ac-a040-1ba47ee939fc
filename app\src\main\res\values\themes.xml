<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.WaterMeterApp" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="colorError">@color/error</item>
        <!-- 强制明亮背景 -->
        <item name="android:windowBackground">@color/light_background</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/primary_text</item>
        <item name="colorOnBackground">@color/primary_text</item>
        <item name="android:textColorPrimary">@color/primary_text</item>
        <item name="android:textColorSecondary">@color/secondary_text</item>
    </style>

    <!-- 欢迎界面主题 -->
    <style name="Theme.WaterMeterApp.Welcome" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">@color/primary</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowBackground">@color/light_background</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/primary_text</item>
    </style>

    <!-- 登录界面主题 -->
    <style name="Theme.WaterMeterApp.Login" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:statusBarColor">@color/primary</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowBackground">@color/light_background</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/primary_text</item>
        <!-- 确保内容能够延伸到屏幕边缘 -->
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>

    <!-- NumberPicker样式 -->
    <style name="NumberPickerStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/primary_text</item>
        <item name="android:colorControlNormal">@color/primary</item>
    </style>
</resources>