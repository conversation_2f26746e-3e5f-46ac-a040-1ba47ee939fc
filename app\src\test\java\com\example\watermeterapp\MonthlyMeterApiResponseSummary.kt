package com.example.watermeterapp

/**
 * 月度抄表API响应数据总结
 * 
 * 这个文件总结了服务器API返回的月度抄表数据结构和内容
 */
object MonthlyMeterApiResponseSummary {

    /**
     * 获取所有月抄表类型 (getMonthlyMeterTypes)
     * GET /api/meters/types/monthly
     * 
     * 响应示例:
     * ["隔油间", "生活水泵房", "中水机房"]
     */
    val allMonthlyMeterTypesResponse = """
    ["隔油间", "生活水泵房", "中水机房"]
    """.trimIndent()
    
    /**
     * 获取月抄表类型及子模块 (getMonthlyMeterTypesWithSubTypes)
     * GET /api/meters/types/monthly-with-subtypes
     * 
     * 响应示例:
     */
    val monthlyMeterTypesWithSubTypesResponse = """
    {
        "code": 0,
        "msg": "获取成功",
        "data": [
            {
                "parentType": "隔油间",
                "subTypes": [
                    {
                        "name": "1#隔油间",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#隔油间",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "3#隔油间",
                        "hasNoMeter": true,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false
            },
            {
                "parentType": "生活水泵房",
                "subTypes": [
                    {
                        "name": "1#水泵",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#水泵",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false
            },
            {
                "parentType": "中水机房",
                "subTypes": [
                    {
                        "name": "进水总表",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "出水总表",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false
            }
        ]
    }
    """.trimIndent()
    
    /**
     * 获取特定月抄表类型的子模块 (getMonthlySubTypes)
     * GET /api/meters/types/monthly-subtypes?parentType=隔油间
     * 
     * 响应示例:
     */
    val monthlySubTypesResponse = """
    {
        "parentType": "隔油间",
        "subTypes": [
            {
                "name": "1#隔油间",
                "hasNoMeter": false,
                "isSubmitted": false,
                "isEnabled": true,
                "isReactivated": false
            },
            {
                "name": "2#隔油间",
                "hasNoMeter": false,
                "isSubmitted": false,
                "isEnabled": true,
                "isReactivated": false
            },
            {
                "name": "3#隔油间",
                "hasNoMeter": true,
                "isSubmitted": false,
                "isEnabled": true,
                "isReactivated": false
            }
        ],
        "expanded": false
    }
    """.trimIndent()
    
    /**
     * 月度抄表数据结构总结
     */
    fun getSummary(): String {
        val sb = StringBuilder()
        
        sb.appendLine("===== 月度抄表API响应数据总结 =====")
        sb.appendLine()
        
        sb.appendLine("1. 获取所有月抄表类型 (getMonthlyMeterTypes)")
        sb.appendLine("   - 接口: GET /api/meters/types/monthly")
        sb.appendLine("   - 返回: 字符串数组，包含所有月度抄表类型名称")
        sb.appendLine("   - 示例: [\"隔油间\", \"生活水泵房\", \"中水机房\"]")
        sb.appendLine()
        
        sb.appendLine("2. 获取月抄表类型及子模块 (getMonthlyMeterTypesWithSubTypes)")
        sb.appendLine("   - 接口: GET /api/meters/types/monthly-with-subtypes")
        sb.appendLine("   - 返回: 包含所有月度抄表类型及其子类型的详细信息")
        sb.appendLine("   - 数据结构:")
        sb.appendLine("     - code: 状态码，0表示成功")
        sb.appendLine("     - msg: 状态消息")
        sb.appendLine("     - data: 数组，包含所有月度抄表类型")
        sb.appendLine("       - parentType: 父类型名称")
        sb.appendLine("       - subTypes: 子类型数组")
        sb.appendLine("         - name: 子类型名称")
        sb.appendLine("         - hasNoMeter: 是否无表项")
        sb.appendLine("         - isSubmitted: 是否已提交")
        sb.appendLine("         - isEnabled: 是否启用")
        sb.appendLine("         - isReactivated: 是否重新激活")
        sb.appendLine("       - expanded: 是否展开")
        sb.appendLine()
        
        sb.appendLine("3. 获取特定月抄表类型的子模块 (getMonthlySubTypes)")
        sb.appendLine("   - 接口: GET /api/meters/types/monthly-subtypes?parentType={parentType}")
        sb.appendLine("   - 参数: parentType - 父类型名称")
        sb.appendLine("   - 返回: 特定月度抄表类型的详细信息，包含其所有子类型")
        sb.appendLine()
        
        sb.appendLine("===== 月度抄表数据内容总结 =====")
        sb.appendLine()
        
        sb.appendLine("服务器返回的月度抄表类型共有3个:")
        sb.appendLine("1. 隔油间 (3个子类型)")
        sb.appendLine("   - 1#隔油间")
        sb.appendLine("   - 2#隔油间")
        sb.appendLine("   - 3#隔油间 [无表]")
        sb.appendLine("2. 生活水泵房 (2个子类型)")
        sb.appendLine("   - 1#水泵")
        sb.appendLine("   - 2#水泵")
        sb.appendLine("3. 中水机房 (2个子类型)")
        sb.appendLine("   - 进水总表")
        sb.appendLine("   - 出水总表")
        
        return sb.toString()
    }
} 