package com.example.watermeterapp.data.api.adapter

import com.google.gson.TypeAdapter
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import com.google.gson.stream.JsonWriter
import com.example.watermeterapp.data.model.ApiResponse
import com.example.watermeterapp.data.model.User

/**
 * 字符串响应适配器
 * 处理服务器返回的字符串响应，并尝试解析为User对象
 */
class StringResponseAdapter : TypeAdapter<ApiResponse<User>>() {
    
    override fun write(out: JsonWriter, value: ApiResponse<User>?) {
        // 写入操作不需要实现
        if (value == null) {
            out.nullValue()
            return
        }
        
        out.beginObject()
        out.name("data")
        if (value.data == null) {
            out.nullValue()
        } else {
            out.beginObject()
            out.name("phoneNumber").value(value.data.phoneNumber)
            out.name("name").value(value.data.name)
            out.name("isAuthorized").value(value.data.isAuthorized)
            out.name("department").value(value.data.department)
            out.name("role").value(value.data.role)
            out.endObject()
        }
        out.name("message").value(value.message)
        out.name("success").value(value.success)
        out.name("code").value(value.code)
        out.endObject()
    }
    
    override fun read(reader: JsonReader): ApiResponse<User> {
        val token = reader.peek()
        
        try {
            // 如果是字符串，尝试解析字符串内容
            if (token == JsonToken.STRING) {
                val responseStr = reader.nextString()
                
                // 根据字符串内容创建用户对象
                // 之前的判断逻辑存在问题，可能错误解析
                // 明确检测"denied"字样优先级高于检测"authorized"
                val isAuthorized = if (responseStr.contains("denied", ignoreCase = true) || 
                                       responseStr.contains("unauthorized", ignoreCase = true) ||
                                       responseStr.contains("not authorized", ignoreCase = true)) {
                    false
                } else if (responseStr.contains("authorized", ignoreCase = true) || 
                          responseStr.contains("success", ignoreCase = true)) {
                    true
                } else {
                    // 如果没有明确的授权或未授权关键字，默认为未授权
                    false
                }
                
                // 从字符串中尝试提取电话号码
                val phoneRegex = "\\d{11}".toRegex()
                val phoneMatch = phoneRegex.find(responseStr)
                val phone = phoneMatch?.value ?: ""
                
                // 创建用户对象
                val user = User(
                    phoneNumber = phone,
                    name = "用户_$phone",
                    isAuthorized = isAuthorized
                )
                
                return ApiResponse(
                    data = user,
                    message = responseStr,
                    success = isAuthorized
                )
            }
            // 如果是null，返回空响应
            else if (token == JsonToken.NULL) {
                reader.nextNull()
                return ApiResponse(
                    data = null,
                    message = "空响应",
                    success = false,
                    code = 500
                )
            }
            // 如果是对象，尝试正常解析
            else if (token == JsonToken.BEGIN_OBJECT) {
                reader.beginObject()
                
                var data: User? = null
                var message: String? = null
                var success = false
                var code = 500
                
                while (reader.hasNext()) {
                    val name = reader.nextName()
                    
                    when (name) {
                        "data" -> {
                            if (reader.peek() == JsonToken.BEGIN_OBJECT) {
                                reader.beginObject()
                                
                                var phoneNumber = ""
                                var userName = ""
                                var isAuthorized = false
                                var department = ""
                                var role = "抄表员"
                                var token: String? = null
                                var authLevel: Int? = null
                                
                                while (reader.hasNext()) {
                                    val fieldName = reader.nextName()
                                    
                                    when (fieldName) {
                                        "phoneNumber" -> phoneNumber = reader.nextString()
                                        "name" -> userName = reader.nextString()
                                        "isAuthorized" -> {
                                            isAuthorized = when (reader.peek()) {
                                                JsonToken.BOOLEAN -> reader.nextBoolean()
                                                JsonToken.NUMBER -> reader.nextInt() != 0
                                                JsonToken.STRING -> {
                                                    val value = reader.nextString()
                                                    value.equals("true", ignoreCase = true) || value == "1"
                                                }
                                                else -> {
                                                    reader.skipValue()
                                                    false
                                                }
                                            }
                                        }
                                        "department" -> department = reader.nextString()
                                        "role" -> role = reader.nextString()
                                        "token" -> token = reader.nextString()
                                        "authLevel" -> authLevel = reader.nextInt()
                                        else -> reader.skipValue()
                                    }
                                }
                                
                                reader.endObject()
                                data = User(phoneNumber, userName, isAuthorized, department, role, token, authLevel)
                            } else if (reader.peek() == JsonToken.NULL) {
                                reader.nextNull()
                                data = null
                            } else {
                                reader.skipValue()
                            }
                        }
                        "message" -> message = reader.nextString()
                        "success" -> success = reader.nextBoolean()
                        "code" -> code = reader.nextInt()
                        else -> reader.skipValue()
                    }
                }
                
                reader.endObject()
                return ApiResponse(data, message, success, code)
            }
            // 其他类型，跳过并返回空响应
            else {
                reader.skipValue()
                return ApiResponse(
                    data = null,
                    message = "不支持的响应格式",
                    success = false,
                    code = 400
                )
            }
        } catch (e: Exception) {
            // 尝试读取剩余内容作为字符串
            try {
                val remainingContent = reader.nextString()
                
                return ApiResponse(
                    data = User("unknown", "未知用户", false),
                    message = "解析错误: ${e.message}, 响应内容: $remainingContent",
                    success = false,
                    code = 400
                )
            } catch (e2: Exception) {
                // 完全失败，返回错误响应
                return ApiResponse(
                    data = null,
                    message = "无法解析服务器响应: ${e.message}",
                    success = false,
                    code = 500
                )
            }
        }
    }
} 