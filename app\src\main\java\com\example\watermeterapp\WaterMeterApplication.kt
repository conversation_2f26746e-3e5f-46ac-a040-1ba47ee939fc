package com.example.watermeterapp

import android.app.Application
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatDelegate
import com.example.watermeterapp.data.api.ApiClient
import com.example.watermeterapp.data.api.ApiConfig
import com.example.watermeterapp.utils.UpdateManager

/**
 * 水表应用程序类
 * 用于在应用启动时初始化全局配置
 */
class WaterMeterApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()

        // 强制使用明亮模式，禁用深色模式
        AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)

        // 初始化API配置
        ApiConfig.initialize(this)

        // 设置ApiClient应用上下文，用于JWT认证
        ApiClient.setContext(this)
        
        // 延迟3秒检查旧版本，避免影响应用启动速度
        Handler(Looper.getMainLooper()).postDelayed({
            // 检查并提示卸载旧版本
            UpdateManager.checkAndUninstallOldVersion(this)
        }, 3000)
    }
} 
