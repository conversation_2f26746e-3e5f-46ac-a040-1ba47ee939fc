# 水表抄录系统 ProGuard 配置

# 保持应用程序的所有类不被混淆（最安全的方式）
-keep class com.example.watermeterapp.** { *; }

# 保持数据模型类不被混淆
-keep class com.example.watermeterapp.data.model.** { *; }

# 保持API接口不被混淆
-keep interface com.example.watermeterapp.data.api.** { *; }

# 保持UI类不被混淆
-keep class com.example.watermeterapp.ui.** { *; }

# 保持序列化相关
-keepnames class * implements java.io.Serializable
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    !private <fields>;
    !private <methods>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保持Activity类不被混淆
-keep public class * extends android.app.Activity
-keep public class * extends androidx.appcompat.app.AppCompatActivity

# 保持所有Activity的onCreate方法
-keepclassmembers class * extends android.app.Activity {
    protected void onCreate(android.os.Bundle);
    protected void onStart();
    protected void onResume();
    protected void onPause();
    protected void onStop();
    protected void onDestroy();
}

# 保持Intent相关
-keep class android.content.Intent { *; }
-keepclassmembers class android.content.Intent {
    *;
}

# 保持Bundle相关
-keep class android.os.Bundle { *; }
-keepclassmembers class android.os.Bundle {
    *;
}

# 保持ViewBinding相关
-keep class * extends androidx.viewbinding.ViewBinding {
    *;
}

# 保持更新相关类
-keep class com.example.watermeterapp.data.model.UpdateInfo { *; }
-keep class com.example.watermeterapp.data.model.VersionCheckResponse { *; }
-keep class com.example.watermeterapp.utils.UpdateManager { *; }

# 保持API接口不被混淆
-keep interface com.example.watermeterapp.data.api.** { *; }

# Retrofit相关 - 更严格的保护规则
-keepattributes Signature, InnerClasses, EnclosingMethod
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations
-keepattributes *Annotation*

# 保持Retrofit核心类
-keep class retrofit2.** { *; }
-keep interface retrofit2.** { *; }
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

# 保持Call接口和相关类
-keep interface retrofit2.Call
-keep class retrofit2.Call { *; }
-keep class retrofit2.Response { *; }
-keep class retrofit2.Callback { *; }

# 保持我们的API接口不被混淆
-keep interface com.example.watermeterapp.data.api.ApiService { *; }
-keepclassmembers interface com.example.watermeterapp.data.api.ApiService {
    *;
}

-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-dontwarn javax.annotation.**
-dontwarn kotlin.Unit
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

# Gson相关 - 增强保护
-keepattributes Signature
-keepattributes *Annotation*
-keepattributes EnclosingMethod
-keepattributes InnerClasses
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# 保护泛型类型信息
-keepattributes Signature
-keep class com.google.gson.reflect.TypeToken { *; }
-keep class * extends com.google.gson.reflect.TypeToken

# OkHttp相关
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Material Design组件
-keep class com.google.android.material.** { *; }
-dontwarn com.google.android.material.**
-dontnote com.google.android.material.**

# AndroidX相关
-keep class androidx.** { *; }
-keep interface androidx.** { *; }
-dontwarn androidx.**

# 保持调试信息（可选，生产环境可以注释掉）
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile