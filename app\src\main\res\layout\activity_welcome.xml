<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/welcome_background"
    tools:context=".ui.welcome.WelcomeActivity">

    <!-- 顶部装饰 -->
    <View
        android:id="@+id/topDecoration"
        android:layout_width="0dp"
        android:layout_height="200dp"
        android:background="@drawable/welcome_top_decoration"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 应用图标 -->
    <ImageView
        android:id="@+id/ivAppIcon"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="100dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/ic_water_meter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 欢迎文字 -->
    <TextView
        android:id="@+id/tvWelcome"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="@string/welcome_title"
        android:textColor="@color/white"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivAppIcon" />

    <!-- 用户信息卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardUserInfo"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="48dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvWelcome">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_user"
                    android:tint="@color/primary" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvUserName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/primary_text"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        tools:text="张三" />

                    <TextView
                        android:id="@+id/tvUserInfo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/secondary_text"
                        android:textSize="14sp"
                        tools:text="水务管理部 · 抄表员" />

                </LinearLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/tvPhoneNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:textColor="@color/secondary_text"
                android:textSize="14sp"
                tools:text="手机号：13800138000" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 进入按钮 -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnEnter"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="48dp"
        android:text="@string/enter_app"
        android:textSize="16sp"
        app:cornerRadius="28dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cardUserInfo" />

    <!-- 底部装饰 -->
    <View
        android:layout_width="0dp"
        android:layout_height="100dp"
        android:background="@drawable/welcome_bottom_decoration"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
