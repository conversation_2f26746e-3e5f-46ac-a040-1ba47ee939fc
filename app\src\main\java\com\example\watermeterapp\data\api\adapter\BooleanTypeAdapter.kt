package com.example.watermeterapp.data.api.adapter

import com.google.gson.TypeAdapter
import com.google.gson.stream.JsonReader
import com.google.gson.stream.JsonToken
import com.google.gson.stream.JsonWriter

/**
 * 自定义布尔值适配器，支持将数字(1/0)转换为布尔值
 */
class BooleanTypeAdapter : TypeAdapter<Boolean>() {
    
    override fun write(out: JsonWriter, value: Boolean?) {
        if (value == null) {
            out.nullValue()
        } else {
            out.value(value)
        }
    }
    
    override fun read(reader: JsonReader): Boolean {
        val token = reader.peek()
        return when (token) {
            JsonToken.BOOLEAN -> reader.nextBoolean()
            JsonToken.NUMBER -> reader.nextInt() != 0
            JsonToken.STRING -> {
                val stringValue = reader.nextString()
                stringValue.equals("true", ignoreCase = true) || stringValue == "1"
            }
            JsonToken.NULL -> {
                reader.nextNull()
                false
            }
            else -> {
                reader.skipValue()
                false
            }
        }
    }
} 