package com.example.watermeterapp

/**
 * 月度抄表API响应数据主类
 * 用于打印月度抄表API响应数据总结
 */
object MonthlyMeterApiResponseMain {
    
    /**
     * 主函数
     */
    @JvmStatic
    fun main(args: Array<String>) {
        println("===== 服务器月度抄表API响应数据 =====")
        println("服务器地址: http://223.76.187.251:3002/api/")
        println("用户手机号: 13995944332")
        println()
        
        // 打印API响应数据总结
        println(MonthlyMeterApiResponseSummary.getSummary())
        
        println()
        println("===== 获取所有月抄表类型 (getMonthlyMeterTypes) =====")
        println("请求: GET /api/meters/types/monthly")
        println("响应:")
        println(MonthlyMeterApiResponseSummary.allMonthlyMeterTypesResponse)
        
        println()
        println("===== 获取月抄表类型及子模块 (getMonthlyMeterTypesWithSubTypes) =====")
        println("请求: GET /api/meters/types/monthly-with-subtypes")
        println("响应:")
        println(MonthlyMeterApiResponseSummary.monthlyMeterTypesWithSubTypesResponse)
        
        println()
        println("===== 获取特定月抄表类型的子模块 (getMonthlySubTypes) =====")
        println("请求: GET /api/meters/types/monthly-subtypes?parentType=隔油间")
        println("响应:")
        println(MonthlyMeterApiResponseSummary.monthlySubTypesResponse)
    }
} 