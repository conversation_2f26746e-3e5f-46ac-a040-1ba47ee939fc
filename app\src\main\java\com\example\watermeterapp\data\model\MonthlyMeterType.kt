package com.example.watermeterapp.data.model

/**
 * 月抄表类型数据模型，支持子模块
 */
data class MonthlyMeterType(
    val parentType: String, // 主类型名称，如"隔油间"
    val subTypes: List<SubMeterType> = emptyList(), // 子类型列表
    var expanded: Boolean = false // 新增展开/折叠状态
) {
    /**
     * 子模块类型
     */
    data class SubMeterType(
        val name: String, // 子模块名称，如"1#隔油间"
        val hasNoMeter: Boolean = false, // 是否无表，根据BeiZhu字段判断
        var isSubmitted: Boolean = false, // 是否已提交
        var isEnabled: Boolean = true, // 是否启用（用于双击激活/禁用）
        var isReactivated: Boolean = false // 是否已重新激活（长按后激活）
    )
} 