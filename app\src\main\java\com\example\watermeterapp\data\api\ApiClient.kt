﻿package com.example.watermeterapp.data.api

import android.content.Context
import com.example.watermeterapp.BuildConfig
import com.example.watermeterapp.data.api.adapter.BooleanTypeAdapter
import com.example.watermeterapp.data.api.adapter.MonthlyMeterTypeAdapter
import com.example.watermeterapp.data.api.adapter.MonthlyMeterTypeListDeserializer
import com.example.watermeterapp.data.api.adapter.StringListDeserializer
import com.example.watermeterapp.data.api.adapter.UserDeserializer
import com.example.watermeterapp.data.api.adapter.StringResponseAdapter
import com.example.watermeterapp.data.api.converter.LenientGsonConverterFactory
import com.example.watermeterapp.data.model.ApiResponse
import com.example.watermeterapp.data.model.MeterReading
import com.example.watermeterapp.data.model.MonthlyMeterType
import com.example.watermeterapp.data.model.User
import com.example.watermeterapp.utils.PrefsUtil
import okhttp3.Interceptor
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Retrofit
import java.util.concurrent.TimeUnit
import com.google.gson.GsonBuilder
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * API客户端
 * 用于创建和管理API服务
 */
object ApiClient {
    private const val TAG = "ApiClient"
    private var appContext: Context? = null
    private var customServerUrl: String? = null
    private var retrofit: Retrofit? = null
    private var apiServiceInstance: ApiService? = null

    /**
     * 设置应用上下文
     */
    fun setContext(context: Context) {
        appContext = context.applicationContext
    }

    /**
     * 设置自定义服务器地址
     */
    fun setCustomServerUrl(url: String?) {
        val oldUrl = customServerUrl
        
        // 清理URL格式
        val cleanUrl = url?.trim()?.let { urlStr ->
            if (urlStr.isEmpty()) {
                null
            } else {
                // 确保URL以http://开头
                if (!urlStr.startsWith("http://") && !urlStr.startsWith("https://")) {
                    "http://$urlStr"
                } else {
                    urlStr
                }
            }
        }
        
        customServerUrl = cleanUrl
        
        // 如果URL发生变化，重置Retrofit实例
        if (oldUrl != customServerUrl) {
            retrofit = null
            apiServiceInstance = null
        }
    }

    /**
     * 获取自定义服务器地址
     */
    fun getCustomServerUrl(): String? = customServerUrl

    /**
     * 获取Retrofit基础URL
     */
    fun getRetrofitBaseUrl(): String = baseUrl

    /**
     * 获取基础URL
     */
    private val baseUrl: String
        get() {
            // 如果设置了自定义服务器地址，优先使用（仅在Debug模式下）
            if (BuildConfig.SHOW_SERVER_CONFIG && !customServerUrl.isNullOrBlank()) {
                return customServerUrl!!
            }

            // 使用BuildConfig中配置的服务器地址
            return BuildConfig.BASE_URL
    }
    
    // 创建Retrofit实例
    private fun createRetrofit(): Retrofit {
        val gson = GsonBuilder()
            .setLenient()
            .registerTypeAdapter(Boolean::class.java, BooleanTypeAdapter())
            .registerTypeAdapter(Boolean::class.javaObjectType, BooleanTypeAdapter())
            .registerTypeAdapter(MonthlyMeterType::class.java, MonthlyMeterTypeAdapter())
            .registerTypeAdapter(User::class.java, UserDeserializer())
            .registerTypeAdapter(
                object : com.google.gson.reflect.TypeToken<ApiResponse<User>>() {}.type,
                StringResponseAdapter()
            )
            .registerTypeAdapter(
                object : com.google.gson.reflect.TypeToken<List<String>>() {}.type,
                StringListDeserializer()
            )
            .registerTypeAdapter(
                object : com.google.gson.reflect.TypeToken<List<MonthlyMeterType>>() {}.type,
                MonthlyMeterTypeListDeserializer()
            )
            .create()
            
        val authInterceptor = Interceptor { chain ->
            val originalRequest = chain.request()
            val token = appContext?.let { PrefsUtil.getJwtToken(it) }
            
            val request = if (!token.isNullOrEmpty()) {
                originalRequest.newBuilder()
                    .header("Authorization", "Bearer $token")
                    .build()
            } else {
                originalRequest
            }
            
            try {
                val response = chain.proceed(request)
                
                if (!response.isSuccessful && response.code in 400..499) {
                    // 处理认证错误等
                }
                
                response
            } catch (e: Exception) {
                when (e) {
                    is UnknownHostException -> { /* 网络错误：无法连接服务器 */ }
                    is SocketTimeoutException -> { /* 网络错误：连接超时 */ }
                    is IOException -> { /* 网络错误：IO异常 */ }
                    else -> { /* 网络错误：未知错误 */ }
                }
                throw e
            }
        }
        
        val client = OkHttpClient.Builder()
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(15, TimeUnit.SECONDS)
            .writeTimeout(15, TimeUnit.SECONDS)
            .addInterceptor(authInterceptor)
            .build()
            
        val finalBaseUrl = baseUrl
        
        return Retrofit.Builder()
            .baseUrl(finalBaseUrl)
            .client(client)
            .addConverterFactory(LenientGsonConverterFactory.create(gson))
            .build()
    }

    /**
     * 获取API服务实例
     */
    val apiService: ApiService
        get() {
            if (retrofit == null) {
                retrofit = createRetrofit()
            }
            
            if (apiServiceInstance == null) {
                apiServiceInstance = retrofit!!.create(ApiService::class.java)
            }
            
            return apiServiceInstance!!
        }
    
    /**
     * 测试服务器连接
     */
    fun testServerConnection(url: String, callback: (Boolean) -> Unit) {
        val testUrl = if (!url.startsWith("http://") && !url.startsWith("https://")) {
            "http://$url"
        } else {
            url
        }
        
        val client = OkHttpClient.Builder()
            .connectTimeout(5, TimeUnit.SECONDS)
            .readTimeout(5, TimeUnit.SECONDS)
            .build()
        
        // 创建一个简单的GET请求来测试连接
        val request = okhttp3.Request.Builder()
            .url(testUrl)
            .get()
            .build()
            
        client.newCall(request).enqueue(object : okhttp3.Callback {
            override fun onResponse(call: okhttp3.Call, response: okhttp3.Response) {
                // 只要能获得响应（无论状态码），就认为服务器可达
                callback(true)
            }
            
            override fun onFailure(call: okhttp3.Call, e: java.io.IOException) {
                callback(false)
            }
        })
    }
}
