<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_background"
    android:fillViewport="true"
    android:padding="16dp"
    android:paddingBottom="32dp"
    android:clipToPadding="false"
    tools:context=".ui.main.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 用户信息栏 -->
        <LinearLayout
            android:id="@+id/layoutUserInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_user"
                android:tint="@color/primary" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvUserName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/primary_text"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:text="用户" />

                <TextView
                    android:id="@+id/tvUserRole"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/secondary_text"
                    android:textSize="14sp"
                    android:text="抄表员" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvMainTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_gravity="center_horizontal"
            android:text="@string/main_title"
            android:textColor="@color/primary_text"
            android:textSize="24sp"
            android:textStyle="bold" />

        <!-- 日抄表卡片 -->
        <LinearLayout
            android:id="@+id/cardDailyReading"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_marginTop="48dp"
            android:background="@color/card_daily"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="24dp"
            android:elevation="4dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/ivDailyIcon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_daily_reading"
                android:tint="@color/white" />

            <TextView
                android:id="@+id/tvDailyReadingTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="@string/daily_reading"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:contentDescription="@string/daily_reading"
                android:src="@drawable/ic_launcher_foreground" />

        </LinearLayout>

        <!-- 月抄表卡片 -->
        <LinearLayout
            android:id="@+id/cardMonthlyReading"
            android:layout_width="match_parent"
            android:layout_height="160dp"
            android:layout_marginTop="24dp"
            android:background="@color/card_monthly"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="24dp"
            android:elevation="4dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:id="@+id/ivMonthlyIcon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_monthly_reading"
                android:tint="@color/white" />

            <TextView
                android:id="@+id/tvMonthlyReadingTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="@string/monthly_reading"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivMonthlyRightIcon"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:src="@drawable/ic_water_meter"
                android:tint="@color/white"
                android:alpha="0.8" />

        </LinearLayout>

        <!-- 底部间距 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="32dp" />

    </LinearLayout>

</ScrollView>
