﻿package com.example.watermeterapp.ui.welcome

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import com.example.watermeterapp.data.model.User
import com.example.watermeterapp.databinding.ActivityWelcomeBinding
import com.example.watermeterapp.ui.main.MainActivity

class WelcomeActivity : AppCompatActivity() {

    private lateinit var binding: ActivityWelcomeBinding
    private lateinit var currentUser: User
    private var hasNavigated = false // 防止重复导航
    private var autoNavigateHandler: Handler? = null

    companion object {
        const val EXTRA_USER = "extra_user"
        const val WELCOME_DELAY = 2000L // 2秒后自动跳转
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityWelcomeBinding.inflate(layoutInflater)
            setContentView(binding.root)

            // 获取用户信息
            val userPhone = intent.getStringExtra("user_phone") ?: ""
            val userName = intent.getStringExtra("user_name") ?: "未知用户"
            val userAuthorized = intent.getBooleanExtra("user_authorized", false)
            val userDepartment = intent.getStringExtra("user_department") ?: ""
            val userRole = intent.getStringExtra("user_role") ?: "抄表员"
            
            currentUser = User(
                phoneNumber = userPhone,
                name = userName,
                isAuthorized = userAuthorized,
                department = userDepartment,
                role = userRole
            )
            
        } catch (e: Exception) {
            // 如果出错，创建默认用户
            currentUser = User("", "未知用户", false)
        }

        setupUI()
        setupClickListeners()

        // 自动跳转到主界面
        autoNavigateHandler = Handler(Looper.getMainLooper())
        autoNavigateHandler?.postDelayed({
            navigateToMain()
        }, WELCOME_DELAY)
    }

    private fun setupUI() {
        // 显示用户名，优先显示真实姓名，否则显示手机号
        val displayName = if (currentUser.name.isNotBlank() && currentUser.name != "未知用户") {
            currentUser.name
        } else {
            currentUser.phoneNumber
        }
        
        binding.tvUserName.text = displayName
        binding.tvUserInfo.text = "${currentUser.department}  ${currentUser.role}"
        binding.tvPhoneNumber.text = "手机号：${currentUser.phoneNumber}"
    }

    private fun setupClickListeners() {
        binding.btnEnter.setOnClickListener {
            navigateToMain()
        }
    }

    private fun navigateToMain() {
        // 防止重复导航
        if (hasNavigated) {
            return
        }
        hasNavigated = true

        // 取消自动跳转
        autoNavigateHandler?.removeCallbacksAndMessages(null)

        val intent = Intent(this, MainActivity::class.java).apply {
            putExtra("user_phone", currentUser.phoneNumber)
            putExtra("user_name", currentUser.name)
            putExtra("user_authorized", currentUser.isAuthorized)
            putExtra("user_department", currentUser.department)
            putExtra("user_role", currentUser.role)
        }
        startActivity(intent)
        finish()

        // 添加过渡动画
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out)
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理Handler，防止内存泄漏
        autoNavigateHandler?.removeCallbacksAndMessages(null)
    }
}
