package com.example.watermeterapp.data.api.adapter

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonParseException
import java.lang.reflect.Type

/**
 * 字符串列表的JSON反序列化器
 * 用于处理不同格式的字符串列表数据
 */
class StringListDeserializer : JsonDeserializer<List<String>> {
    
    override fun deserialize(
        json: JsonElement?, 
        typeOfT: Type?, 
        context: JsonDeserializationContext?
    ): List<String> {
        try {
            if (json == null) {
                return emptyList()
            }
            
            // 处理字符串响应（服务器可能返回纯文本）
            if (json.isJsonPrimitive && json.asJsonPrimitive.isString) {
                val responseStr = json.asString
                
                // 如果是逗号分隔的列表，分割并返回
                if (responseStr.contains(",")) {
                    val items = responseStr.split(",").map { it.trim() }
                    return items
                }
                
                // 如果是单个项目，返回单项列表
                return listOf(responseStr)
            }
            
            // 处理JSON数组
            if (json.isJsonArray) {
                val result = mutableListOf<String>()
                val jsonArray = json.asJsonArray
                
                for (element in jsonArray) {
                    if (element.isJsonPrimitive && element.asJsonPrimitive.isString) {
                        result.add(element.asString)
                    }
                }
                
                return result
            }
            
            // 无法识别的JSON格式
            return emptyList()
            
        } catch (e: Exception) {
            throw JsonParseException("解析字符串列表失败", e)
        }
    }
} 