<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/meterLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:padding="16dp">

        <TextView
            android:id="@+id/tvMeterName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/primary_text"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/tvBadge"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- 徽章显示已提交数量 -->
        <TextView
            android:id="@+id/tvBadge"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@drawable/badge_background"
            android:gravity="center"
            android:text="0"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/ivExpand"
            app:layout_constraintTop_toTopOf="@id/tvMeterName"
            app:layout_constraintBottom_toBottomOf="@id/tvMeterName" />

        <ImageView
            android:id="@+id/ivExpand"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:contentDescription="@string/expand_icon"
            android:src="@drawable/ic_expand_more"
            app:layout_constraintBottom_toBottomOf="@id/tvMeterName"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvMeterName" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView> 