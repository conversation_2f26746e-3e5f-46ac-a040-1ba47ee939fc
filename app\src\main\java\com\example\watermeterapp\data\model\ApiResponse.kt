package com.example.watermeterapp.data.model

import com.google.gson.annotations.SerializedName

/**
 * 通用API响应包装类
 * 用于处理各种类型的服务器响应
 */
data class ApiResponse<T>(
    @SerializedName("data")
    val data: T? = null,
    
    @SerializedName("message")
    val message: String? = null,
    
    @SerializedName("success")
    val success: Boolean = true,
    
    @SerializedName("code")
    val code: Int = 200
) 