﻿package com.example.watermeterapp.ui.login

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.example.watermeterapp.BuildConfig
import com.example.watermeterapp.R
import com.example.watermeterapp.data.api.ApiClient
import com.example.watermeterapp.data.model.ApiResponse
import com.example.watermeterapp.data.model.User
import com.example.watermeterapp.databinding.ActivityLoginBinding
import com.example.watermeterapp.ui.main.MainActivity
import com.example.watermeterapp.utils.PrefsUtil
import com.example.watermeterapp.utils.UserConfig
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * 登录界面
 */
class LoginActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLoginBinding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 检查是否已经登录
        if (PrefsUtil.hasValidLogin(this)) {
            startActivity(Intent(this, MainActivity::class.java))
            finish()
            return
        }

        // 初始化ApiClient
        ApiClient.setContext(this)

        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 设置服务器配置
        setupServerConfiguration()

        // 设置登录按钮点击事件
        binding.btnLogin.setOnClickListener {
            val phoneNumber = binding.etPhoneNumber.text.toString().trim()
            if (phoneNumber.isNotEmpty()) {
                login(phoneNumber)
            } else {
                Toast.makeText(this, "请输入手机号", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 设置服务器配置
     */
    private fun setupServerConfiguration() {
        // 只在Debug版本中显示服务器配置
        if (BuildConfig.SHOW_SERVER_CONFIG) {
            binding.layoutServerConfig.visibility = View.VISIBLE
            
            // 设置指定服务器地址
            binding.etServerAddress.setText("**************")
            binding.etServerPort.setText("3002")
            
            // 自动保存服务器配置
            saveServerConfiguration()
            
            // 设置保存按钮点击事件
            binding.btnSaveServer.setOnClickListener {
                saveServerConfiguration()
            }
        } else {
            binding.layoutServerConfig.visibility = View.GONE
        }
        
        // 设置默认手机号
        binding.etPhoneNumber.setText("13995944332")
    }

    /**
     * 解析服务器URL获取地址和端口
     */
    private fun parseServerUrl(url: String): Pair<String, String> {
        return try {
            // 移除协议前缀
            var cleanUrl = url.replace("http://", "").replace("https://", "")

            // 移除路径部分（如 /api/）
            val pathIndex = cleanUrl.indexOf('/')
            if (pathIndex > 0) {
                cleanUrl = cleanUrl.substring(0, pathIndex)
            }

            if (cleanUrl.contains(":")) {
                val colonIndex = cleanUrl.indexOf(':')
                val address = cleanUrl.substring(0, colonIndex)
                val port = cleanUrl.substring(colonIndex + 1)

                Pair(address, port)
            } else {
                // 没有端口，使用默认端口
                Pair(cleanUrl, "3002")
            }
        } catch (e: Exception) {
            Pair("*************", "3002")
        }
    }
    
    /**
     * 保存服务器配置
     */
    private fun saveServerConfiguration() {
        val address = binding.etServerAddress.text.toString().trim()
        val port = binding.etServerPort.text.toString().trim()
        
        if (address.isEmpty()) {
            Toast.makeText(this, "请输入服务器地址", Toast.LENGTH_SHORT).show()
            return
        }
        
        if (port.isEmpty()) {
            Toast.makeText(this, "请输入端口号", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 构建完整的服务器URL
        val serverUrl = "http://$address:$port/api/"
        
        // 保存到ApiClient
        ApiClient.setCustomServerUrl(serverUrl)
        
        Toast.makeText(this, "服务器配置已保存: $address:$port", Toast.LENGTH_SHORT).show()
    }

    /**
     * 执行登录
     */
    private fun login(phoneNumber: String) {
        // 显示加载状态
        binding.progressBar.visibility = View.VISIBLE
        binding.btnLogin.isEnabled = false

        // 检查服务器连接
        val serverUrl = ApiClient.getRetrofitBaseUrl()
        ApiClient.testServerConnection(serverUrl) { isServerReachable ->
            runOnUiThread {
                if (!isServerReachable) {
                    binding.progressBar.visibility = View.GONE
                    binding.btnLogin.isEnabled = true
                    Toast.makeText(
                        this@LoginActivity,
                        "无法连接服务器，请检查网络连接和服务器地址",
                        Toast.LENGTH_LONG
                    ).show()
                    return@runOnUiThread
                }

                // 服务器可达，继续登录请求
                performLogin(phoneNumber)
            }
        }
    }

    /**
     * 执行实际的登录请求
     */
    private fun performLogin(phoneNumber: String) {
        // 调用登录API
        ApiClient.apiService.login(phoneNumber).enqueue(object : Callback<ApiResponse<User>> {
            override fun onResponse(call: Call<ApiResponse<User>>, response: Response<ApiResponse<User>>) {
                binding.progressBar.visibility = View.GONE
                binding.btnLogin.isEnabled = true
                
                if (response.isSuccessful) {
                    val apiResponse = response.body()
                    
                    if (apiResponse?.code == 0 && apiResponse.data != null) {
                        val user = apiResponse.data
                        
                        // 保存用户信息到首选项
                        PrefsUtil.savePhoneNumber(this@LoginActivity, phoneNumber)
                        PrefsUtil.saveUserName(this@LoginActivity, user.name ?: "")
                        PrefsUtil.saveUserDepartment(this@LoginActivity, user.department ?: "")
                        PrefsUtil.saveUserRole(this@LoginActivity, user.role ?: "")
                        PrefsUtil.saveUserAuthorized(this@LoginActivity, user.isAuthorized)
                        
                        // 保存JWT Token（如果有）
                        user.token?.let { token ->
                            PrefsUtil.saveJwtToken(this@LoginActivity, token)
                        }
                        
                        // 保存用户信息到配置文件
                        UserConfig.saveUserInfo(this@LoginActivity, user)

                        // 跳转到主界面，传递用户信息
                        val intent = Intent(this@LoginActivity, MainActivity::class.java).apply {
                            putExtra("user_phone", user.phoneNumber)
                            putExtra("user_name", user.name)
                            putExtra("user_authorized", user.isAuthorized)
                            putExtra("user_department", user.department)
                            putExtra("user_role", user.role)
                            putExtra("user_auth_level", user.authLevel ?: 1)
                        }
                        startActivity(intent)
                        finish()
                    } else {
                        Toast.makeText(this@LoginActivity, apiResponse?.message ?: "登录失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    // 登录失败，尝试读取错误信息
                    val errorBody = response.errorBody()?.string()
                    Toast.makeText(this@LoginActivity, "登录失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<ApiResponse<User>>, t: Throwable) {
                binding.progressBar.visibility = View.GONE
                binding.btnLogin.isEnabled = true
                Toast.makeText(this@LoginActivity, "网络错误: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
}
