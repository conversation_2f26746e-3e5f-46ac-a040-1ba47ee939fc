﻿package com.example.watermeterapp.ui.reading

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.example.watermeterapp.R
import com.example.watermeterapp.data.api.ApiClient
import com.example.watermeterapp.data.model.ApiResponse
import com.example.watermeterapp.data.model.MeterReading
import com.example.watermeterapp.databinding.ActivityMeterReadingBinding
import com.example.watermeterapp.utils.ImageUtils
import com.example.watermeterapp.utils.MonthPickerDialog
import com.example.watermeterapp.utils.PermissionUtils
import com.example.watermeterapp.utils.ZoomableImageView
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.ResponseBody
import pub.devrel.easypermissions.EasyPermissions
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.File
import java.util.*

class MeterReadingActivity : AppCompatActivity(), EasyPermissions.PermissionCallbacks {

    companion object {
        const val EXTRA_METER_TYPE = "extra_meter_type"
        const val EXTRA_IS_DAILY = "extra_is_daily"
        const val EXTRA_MONTH = "extra_month" // 月抄表的月份参数
    }

    private lateinit var binding: ActivityMeterReadingBinding
    private var meterType: String = ""
    private var parentType: String = "" // 父类型，如"隔油间"
    private var subType: String = ""   // 子类型，如"1#隔油间"
    private var isDaily: Boolean = true
    private var selectedMonth: Int? = null // 选择的月份 (1-12)
    private var currentPhotoPath: String? = null
    private var previousReading: Double = 0.0
    private var isPhotoFullScreen = false // 追踪照片是否处于全屏模式

    // 相机启动器
    private val cameraLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            currentPhotoPath?.let { path ->
                handleSelectedImage(path)
            }
        }
    }

    // 相册选择器
    private val galleryLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                try {
                    val tempFile = ImageUtils.createImageFile(this)
                    // 从URI复制文件内容到临时文件
                    contentResolver.openInputStream(uri)?.use { input ->
                        tempFile.outputStream().use { output ->
                            input.copyTo(output)
                        }
                    }
                    currentPhotoPath = tempFile.absolutePath
                    handleSelectedImage(currentPhotoPath!!)
                } catch (e: Exception) {
                    Toast.makeText(this, "从相册选择照片失败: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    // 权限请求启动器
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            openCamera()
        } else {
            Toast.makeText(this, "需要相机权限才能拍照", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMeterReadingBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 设置标题栏返回按钮
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // 初始化界面
        initializeViews()
        setupClickListeners()

        // 加载上期读数
        loadLastReading()
    }

    private fun initializeViews() {
        // 获取传递的参数
        val meterTypeParam = intent.getStringExtra(EXTRA_METER_TYPE) ?: ""
        isDaily = intent.getBooleanExtra(EXTRA_IS_DAILY, true)
        selectedMonth = intent.getIntExtra(EXTRA_MONTH, 0).takeIf { it > 0 }

        // 判断是否包含子模块信息 (格式为 "父类型/子类型")
        if (meterTypeParam.contains('/')) {
            val parts = meterTypeParam.split('/')
            parentType = parts[0]
            subType = parts[1]
            meterType = subType // 显示用
        } else {
            meterType = meterTypeParam
        }

        // 设置标题
        if (subType.isNotEmpty()) {
            supportActionBar?.title = "$parentType - $subType"
        } else {
            supportActionBar?.title = meterType
        }

        // 设置界面
        setupUI()
    }

    private fun setupClickListeners() {
        // 拍照按钮点击事件
        binding.btnTakePhoto.setOnClickListener {
            checkCameraPermissionAndTakePhoto()
        }

        // 选择照片按钮点击事件
        binding.btnSelectPhoto.setOnClickListener {
            checkStoragePermissionAndSelectPhoto()
        }

        // 提交按钮点击事件
        binding.btnSubmit.setOnClickListener {
            submitReading()
        }
        
        // 照片预览点击事件 - 实现全屏/恢复切换
        binding.ivPhotoPreview.setOnClickListener {
            togglePhotoFullScreen()
        }
    }

    private fun setupUI() {
        // 如果是子模块，显示完整路径
        if (subType.isNotEmpty()) {
            binding.tvMeterType.text = "$parentType - $subType"
        } else {
            binding.tvMeterType.text = meterType
        }
        
        binding.tvReadingType.text = if (isDaily) "日抄表" else "月抄表"
    }

    /**
     * 检查相机权限并拍照
     */
    private fun checkCameraPermissionAndTakePhoto() {
        if (PermissionUtils.hasCameraPermission(this)) {
            openCamera()
        } else {
            PermissionUtils.requestCameraPermission(this, "需要相机权限来拍摄水表照片")
        }
    }

    /**
     * 检查存储权限并选择照片
     */
    private fun checkStoragePermissionAndSelectPhoto() {
        if (PermissionUtils.hasStoragePermission(this)) {
            openGallery()
        } else {
            PermissionUtils.requestStoragePermission(this, "需要存储权限来选择照片")
        }
    }

    /**
     * 打开相机
     */
    private fun openCamera() {
        try {
            // 生成文件名：类型-表名+时间戳
            val typePrefix = if (isDaily) "日抄表" else "月抄表"
            val meterName = if (subType.isNotEmpty()) subType else meterType
            val timestamp = System.currentTimeMillis()
            val fileName = "$typePrefix-${meterName}$timestamp.jpg"

            val photoFile = ImageUtils.createImageFileWithName(this, fileName)
            currentPhotoPath = photoFile.absolutePath

            val photoURI = FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                photoFile
            )

            val takePictureIntent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
            takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
            cameraLauncher.launch(takePictureIntent)
        } catch (e: Exception) {
            Toast.makeText(this, "无法打开相机: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 打开相册选择图片
     */
    private fun openGallery() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        galleryLauncher.launch(intent)
    }
    
    /**
     * 处理选择或拍摄的图片（添加水印并显示）
     */
    private fun handleSelectedImage(imagePath: String) {
        try {
            // 创建水印标题
            val typePrefix = if (isDaily) "日抄表" else "月抄表"
            val meterName = if (subType.isNotEmpty()) subType else meterType
            val watermarkTitle = "$typePrefix-$meterName"

            // 添加自定义水印
            val processedPath = ImageUtils.addCustomWatermark(this, imagePath, watermarkTitle)
            currentPhotoPath = processedPath

            // 显示照片预览
            binding.ivPhotoPreview.setImageURI(Uri.fromFile(File(processedPath)))
            binding.ivPhotoPreview.visibility = View.VISIBLE
            binding.btnTakePhoto.text = "重新拍照"
        } catch (e: Exception) {
            Toast.makeText(this, "处理照片失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 加载上期读数
     */
    private fun loadLastReading() {
        binding.progressBar.visibility = View.VISIBLE

        // 根据是否有子模块选择不同的API调用
        val apiMeterType = if (subType.isNotEmpty()) {
            // 使用"父类型/子类型"格式调用API
            "$parentType/$subType"
        } else {
            meterType
        }

        ApiClient.apiService.getLastReading(apiMeterType, isDaily).enqueue(object : Callback<MeterReading> {
            override fun onResponse(call: Call<MeterReading>, response: Response<MeterReading>) {
                binding.progressBar.visibility = View.GONE

                if (response.isSuccessful) {
                    val lastReading = response.body()
                    if (lastReading != null && lastReading.readingValue > 0) {
                        previousReading = lastReading.readingValue

                        // 格式化显示信息
                        val readingInfo = StringBuilder()
                        readingInfo.append("上次读数: ${previousReading}")

                        // 如果是日抄表，显示更多信息
                        if (isDaily) {
                            lastReading.readingDate?.let { date ->
                                val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
                                readingInfo.append("\n抄表时间: ${formatter.format(date)}")
                            }
                            lastReading.reader?.let { reader ->
                                if (reader != "系统用户") {
                                    readingInfo.append("\n抄表人: $reader")
                                }
                            }
                        }

                        binding.tvPreviousReading.text = readingInfo.toString()
                    } else {
                        binding.tvPreviousReading.text = "上次读数: 无记录"
                    }
                } else {
                    binding.tvPreviousReading.text = "上次读数: 无记录"
                }
            }

            override fun onFailure(call: Call<MeterReading>, t: Throwable) {
                binding.progressBar.visibility = View.GONE
                binding.tvPreviousReading.text = "上次读数: 获取失败"
                Toast.makeText(
                    this@MeterReadingActivity,
                    "获取上次读数失败: ${t.message}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        })
    }

    /**
     * 提交读数
     */
    private fun submitReading() {
        val readingText = binding.etCurrentReading.text.toString().trim()

        // 验证输入
        if (readingText.isEmpty()) {
            binding.tilCurrentReading.error = "请输入当前读数"
            return
        }

        val currentReading = readingText.toDoubleOrNull()
        if (currentReading == null) {
            binding.tilCurrentReading.error = "请输入有效的数字"
            return
        }

        if (currentReading < previousReading) {
            binding.tilCurrentReading.error = "当前读数不能小于上次读数"
            return
        }

        if (currentPhotoPath == null) {
            Toast.makeText(this, "请先拍照", Toast.LENGTH_SHORT).show()
            return
        }

        // 清除错误提示
        binding.tilCurrentReading.error = null

        // 创建读数对象，使用适当的meterType格式
        val apiMeterType = if (subType.isNotEmpty()) {
            // 使用"父类型/子类型"格式
            "$parentType/$subType"
        } else {
            meterType
        }

        val meterReading = MeterReading(
            meterType = apiMeterType,
            readingValue = currentReading,
            previousReading = previousReading,
            photoPath = currentPhotoPath,
            readingDate = Date(),
            isDaily = isDaily,
            month = selectedMonth // 传递选择的月份
        )

        // 上传读数
        uploadReading(meterReading)
    }

    /**
     * 上传读数
     */
    private fun uploadReading(reading: MeterReading) {
        binding.progressBar.visibility = View.VISIBLE
        binding.btnSubmit.isEnabled = false

        try {
            // 创建照片文件的RequestBody
            val photoFile = File(reading.photoPath!!)
            val requestFile = photoFile.asRequestBody("image/jpeg".toMediaTypeOrNull())

            // 使用文件的实际名称作为上传名称
            val fileName = photoFile.name

            val photoPart = MultipartBody.Part.createFormData("photo", fileName, requestFile)

            // 调用API上传
            ApiClient.apiService.uploadReading(reading, photoPart).enqueue(object : Callback<ResponseBody> {
                override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                    binding.progressBar.visibility = View.GONE
                    binding.btnSubmit.isEnabled = true

                    if (response.isSuccessful) {
                        Toast.makeText(
                            this@MeterReadingActivity,
                            "读数提交成功",
                            Toast.LENGTH_SHORT
                        ).show()

                        // 提交成功后返回结果
                        val resultIntent = Intent().apply {
                            putExtra("parentType", parentType)
                            putExtra("subTypeName", subType)
                        }
                        setResult(Activity.RESULT_OK, resultIntent)
                        finish()
                    } else {
                        Toast.makeText(
                            this@MeterReadingActivity,
                            "提交失败: ${response.code()}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }

                override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                    binding.progressBar.visibility = View.GONE
                    binding.btnSubmit.isEnabled = true

                    Toast.makeText(
                        this@MeterReadingActivity,
                        "网络错误: ${t.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            })
        } catch (e: Exception) {
            binding.progressBar.visibility = View.GONE
            binding.btnSubmit.isEnabled = true

            Toast.makeText(
                this@MeterReadingActivity,
                "错误: ${e.message}",
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    override fun onDestroy() {
        super.onDestroy()
        // 清理临时照片文件
        currentPhotoPath?.let { path ->
            ImageUtils.deleteImageFile(path)
        }
    }

    // 切换照片全屏/原始大小显示
    private fun togglePhotoFullScreen() {
        isPhotoFullScreen = !isPhotoFullScreen
        
        // 隐藏/显示按钮容器
        binding.layoutPhotoButtons.visibility = if (isPhotoFullScreen) View.GONE else View.VISIBLE
        
        if (isPhotoFullScreen) {
            // 切换到全屏模式
            val fullScreenParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT
            )
            
            // 设置约束，使其在父布局中完全居中
            fullScreenParams.topToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
            fullScreenParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
            fullScreenParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
            fullScreenParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
            
            binding.ivPhotoPreview.layoutParams = fullScreenParams
            binding.ivPhotoPreview.scaleType = ImageView.ScaleType.FIT_CENTER
            
            // 启用双指缩放功能
            if (binding.ivPhotoPreview is ZoomableImageView) {
                (binding.ivPhotoPreview as ZoomableImageView).isZoomEnabled = true
            }
            
            // 隐藏其他控件
            binding.tvMeterType.visibility = View.GONE
            binding.tvReadingType.visibility = View.GONE
            binding.tvPreviousReading.visibility = View.GONE
            binding.tvPreviousDate.visibility = View.GONE
            binding.tilCurrentReading.visibility = View.GONE
            binding.btnSubmit.visibility = View.GONE
            
            // 全屏模式下点击照片时提示用户
            Toast.makeText(this, "点击照片返回，支持双指缩放", Toast.LENGTH_SHORT).show()
        } else {
            // 恢复原始大小
            val normalParams = binding.ivPhotoPreview.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
            normalParams.width = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT
            normalParams.height = 200.dpToPx()
            
            // 恢复约束
            normalParams.topToBottom = binding.tilCurrentReading.id
            // 清除全屏时的居中约束
            normalParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
            
            binding.ivPhotoPreview.layoutParams = normalParams
            binding.ivPhotoPreview.scaleType = ImageView.ScaleType.CENTER_INSIDE
            
            // 禁用缩放功能并重置缩放状态
            if (binding.ivPhotoPreview is ZoomableImageView) {
                val zoomableImageView = binding.ivPhotoPreview as ZoomableImageView
                zoomableImageView.isZoomEnabled = false
                zoomableImageView.resetZoom()
            }
            
            // 恢复其他控件显示
            binding.tvMeterType.visibility = View.VISIBLE
            binding.tvReadingType.visibility = View.VISIBLE
            binding.tvPreviousReading.visibility = View.VISIBLE
            binding.tvPreviousDate.visibility = View.VISIBLE
            binding.tilCurrentReading.visibility = View.VISIBLE
            binding.btnSubmit.visibility = View.VISIBLE
        }
    }
    
    // 扩展函数：将dp转换为像素
    private fun Int.dpToPx(): Int {
        val scale = resources.displayMetrics.density
        return (this * scale + 0.5f).toInt()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean("isPhotoFullScreen", isPhotoFullScreen)
        outState.putString("currentPhotoPath", currentPhotoPath)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        isPhotoFullScreen = savedInstanceState.getBoolean("isPhotoFullScreen", false)
        currentPhotoPath = savedInstanceState.getString("currentPhotoPath")
        
        // 如果有照片路径，恢复照片显示
        if (currentPhotoPath != null) {
            binding.ivPhotoPreview.setImageURI(Uri.fromFile(File(currentPhotoPath!!)))
            binding.ivPhotoPreview.visibility = View.VISIBLE
            binding.btnTakePhoto.text = "重新拍照"
            binding.layoutPhotoButtons.visibility = View.VISIBLE // 确保按钮组可见
            
            // 如果之前是全屏模式，恢复全屏状态
            // 注意：这里需要反向切换，因为isPhotoFullScreen已经是目标状态了
            if (isPhotoFullScreen) {
                isPhotoFullScreen = false // 先设置为false，让toggle方法正确切换到全屏
                togglePhotoFullScreen()
            }
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        // 将结果转发给EasyPermissions
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this)
    }

    override fun onPermissionsGranted(requestCode: Int, perms: MutableList<String>) {
        if (requestCode == PermissionUtils.CAMERA_PERMISSION_REQUEST_CODE) {
            openCamera()
        } else if (requestCode == PermissionUtils.STORAGE_PERMISSION_REQUEST_CODE) {
            openGallery()
        }
    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>) {
        if (EasyPermissions.somePermissionPermanentlyDenied(this, perms)) {
            // 用户选择了"不再询问"
            // 可以在这里显示一个对话框，引导用户去设置中手动开启权限
        }
        
        if (requestCode == PermissionUtils.CAMERA_PERMISSION_REQUEST_CODE) {
            Toast.makeText(this, "相机权限被拒绝", Toast.LENGTH_SHORT).show()
        } else if (requestCode == PermissionUtils.STORAGE_PERMISSION_REQUEST_CODE) {
            Toast.makeText(this, "存储权限被拒绝", Toast.LENGTH_SHORT).show()
        }
    }
}
