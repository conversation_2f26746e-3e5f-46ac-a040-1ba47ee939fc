package com.example.watermeterapp.data.api

import android.content.Context
import com.example.watermeterapp.utils.PrefsUtil
import okhttp3.Interceptor
import okhttp3.Response

/**
 * 认证拦截器
 * 自动为需要认证的API请求添加Authorization头
 */
class AuthInterceptor(private val context: Context) : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        val url = originalRequest.url.toString()
        
        // 检查是否是需要认证的接口
        val needsAuth = when {
            url.contains("/meters/types/") -> true
            url.contains("/meters/upload") -> true
            url.contains("/meters/last") -> true
            else -> false
        }
        
        if (!needsAuth) {
            // 不需要认证的接口直接返回
            return chain.proceed(originalRequest)
        }
        
        // 获取保存的JWT token
        val token = PrefsUtil.getJwtToken(context)
        
        if (token.isNullOrEmpty()) {
            // 没有token，直接返回原请求
            return chain.proceed(originalRequest)
        }
        
        // 添加Authorization头
        val authenticatedRequest = originalRequest.newBuilder()
            .header("Authorization", "Bearer $token")
            .build()
        
        return chain.proceed(authenticatedRequest)
    }
}
