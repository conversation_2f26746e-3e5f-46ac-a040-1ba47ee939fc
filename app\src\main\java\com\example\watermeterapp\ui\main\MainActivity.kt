package com.example.watermeterapp.ui.main

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.example.watermeterapp.data.model.User
import com.example.watermeterapp.databinding.ActivityMainBinding
import com.example.watermeterapp.ui.daily.DailyReadingActivity
import com.example.watermeterapp.ui.monthly.MonthlyReadingActivity
import com.example.watermeterapp.utils.PermissionUtils
import com.example.watermeterapp.utils.UpdateManager
import com.example.watermeterapp.utils.NetworkUtils
import com.example.watermeterapp.utils.PrefsUtil
import com.example.watermeterapp.utils.UserConfig
import com.example.watermeterapp.ui.login.LoginActivity
import com.example.watermeterapp.R
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import android.os.Handler
import android.os.Looper
import android.os.Environment

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var currentUser: User
    private var doubleBackToExitPressedOnce = false
    private val doubleClickInterval = 2000 // 两次点击的时间间隔（毫秒）

    companion object {
        const val EXTRA_USER = "extra_user"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        try {
            binding = ActivityMainBinding.inflate(layoutInflater)
            setContentView(binding.root)

            // 初始化视图
            setupViews()
            
            // 设置底部导航
            setupBottomNavigation()
            
            // 加载用户信息
            loadUserInfo()
        } catch (e: Exception) {
            Toast.makeText(this, "应用初始化失败: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun setupViews() {
        // 先从配置文件中获取用户信息
        val userFromConfig = UserConfig.getUserInfo(this)
        
        if (userFromConfig != null) {
            // 使用配置文件中的用户信息
            currentUser = userFromConfig
        } else {
            // 如果配置文件中没有用户信息，则使用Intent中传递的信息
        currentUser = User(
            phoneNumber = intent.getStringExtra("user_phone") ?: "",
            name = intent.getStringExtra("user_name") ?: "未知用户",
            isAuthorized = intent.getBooleanExtra("user_authorized", false),
            department = intent.getStringExtra("user_department") ?: "",
            role = intent.getStringExtra("user_role") ?: "抄表员"
        )
            
            // 将用户信息保存到配置文件中
            UserConfig.saveUserInfo(this, currentUser)
        }

        setupUI()
        setupClickListeners()

        // 请求必要的权限
        PermissionUtils.requestAllPermissions(this)

        // 检查应用更新（延迟3秒执行，避免影响启动体验）
        binding.root.postDelayed({
            UpdateManager.checkUpdate(this, false)
        }, 3000)
    }

    private fun setupUI() {
        binding.tvUserName.text = currentUser.name
        binding.tvUserRole.text = "${currentUser.department} · ${currentUser.role}"
    }

    private fun setupClickListeners() {
        // 设置日抄表卡片点击事件
        binding.cardDailyReading.setOnClickListener {
            val intent = Intent(this, DailyReadingActivity::class.java)
            startActivity(intent)
        }

        // 设置月抄表卡片点击事件
        binding.cardMonthlyReading.setOnClickListener {
            val intent = Intent(this, MonthlyReadingActivity::class.java)
            startActivity(intent)
        }
    }

    private fun setupBottomNavigation() {
        // Implementation of setupBottomNavigation method
    }

    private fun loadUserInfo() {
        // 已在setupViews中加载用户信息
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        
        // 添加退出登录选项
        menu?.add(Menu.NONE, R.id.action_logout, Menu.NONE, "退出登录")
        
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_check_update -> {
                UpdateManager.checkUpdate(this, true)
                true
            }
            R.id.action_logout -> {
                showLogoutConfirmDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    /**
     * 显示退出确认对话框
     */
    private fun showLogoutConfirmDialog() {
        AlertDialog.Builder(this)
            .setTitle("退出登录")
            .setMessage("确定要退出登录吗？")
            .setPositiveButton("确定") { _, _ ->
                logout()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 退出登录
     */
    private fun logout() {
        // 保留登录信息（手机号），但清除其他用户数据
        val phoneNumber = PrefsUtil.getPhoneNumber(this)
        
        // 清除用户配置文件
        UserConfig.clearUserConfig(this)
        
        // 清除首选项中的用户信息，但保留手机号
        PrefsUtil.clearAllUserInfo(this)
        PrefsUtil.savePhoneNumber(this, phoneNumber)
        
        // 跳转到登录界面
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        // 将权限结果传递给EasyPermissions
        pub.devrel.easypermissions.EasyPermissions.onRequestPermissionsResult(
            requestCode, permissions, grantResults, this
        )
    }

    override fun onResume() {
        super.onResume()
        
        // 从配置文件重新加载用户信息，确保从后台返回时用户信息正确
        UserConfig.getUserInfo(this)?.let { user ->
            currentUser = user
            setupUI()
        }
        
        // 检查服务器连接
        NetworkUtils.checkServerConnectionSilent { isConnected ->
            if (!isConnected) {
                runOnUiThread {
                    Toast.makeText(this, "无法连接到服务器，部分功能可能不可用", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    /**
     * 处理返回按键事件
     * 实现双击返回键退出应用
     */
    override fun onBackPressed() {
        if (doubleBackToExitPressedOnce) {
            // 第二次点击，执行退出操作
            super.onBackPressed()
            
            // 清除用户配置文件
            UserConfig.clearUserConfig(this)
            
            // 结束所有Activity
            finishAffinity()
            return
        }

        // 第一次点击
        this.doubleBackToExitPressedOnce = true
        Toast.makeText(this, "再按一次返回键退出应用", Toast.LENGTH_SHORT).show()

        // 2秒后重置标志
        Handler(Looper.getMainLooper()).postDelayed({
            doubleBackToExitPressedOnce = false
        }, doubleClickInterval.toLong())
    }
}
