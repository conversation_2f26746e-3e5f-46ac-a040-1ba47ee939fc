const mysql = require('mysql2');

// 创建数据库连接池
const pool = mysql.createPool({
    host: '**************',  // 远程服务器地址
    user: 'watermeter',
    password: 'qdf823',
    database: 'watermeter',
    waitForConnections: true,
    connectionLimit: 15,      // 增加连接数量
    queueLimit: 0,
    connectTimeout: 60000,   // 60秒连接超时
    acquireTimeout: 60000,   // 60秒获取超时
    timeout: 60000,          // 60秒查询超时
    enableKeepAlive: true,
    keepAliveInitialDelay: 10000, // 10秒保持连接
    charset: 'utf8mb4'
});

// 导出 promise 池，以便可以使用 async/await
const promisePool = pool.promise();

// 数据库连接失败时的错误处理
pool.on('error', (err) => {
    console.error('数据库连接池错误:', err);
    if (err.code === 'PROTOCOL_CONNECTION_LOST') {
        console.error('数据库连接丢失，尝试重新连接');
        // 可以在这里添加自动重连逻辑
    } else {
        throw err;
    }
});

// 测试数据库连接
promisePool.getConnection()
    .then(connection => {
        console.log('✓ 数据库连接成功');
        connection.release();
    })
    .catch(err => {
        console.error('⚠ 数据库连接失败:', err.message);
        console.error('请检查数据库连接配置和确保数据库服务已启动');
    });

module.exports = promisePool;
