package com.example.watermeterapp.ui.monthly

import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.watermeterapp.data.api.ApiClient
import com.example.watermeterapp.data.model.ApiResponse
import com.example.watermeterapp.data.model.MeterReading
import com.example.watermeterapp.data.model.MonthlyMeterType
import com.example.watermeterapp.databinding.ActivityMonthlyReadingBinding
import com.example.watermeterapp.ui.adapter.MonthlyMeterAdapter
import com.example.watermeterapp.ui.reading.MeterReadingActivity
import com.example.watermeterapp.utils.MonthPickerDialog
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import okhttp3.MultipartBody
import okhttp3.ResponseBody
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.*

class MonthlyReadingActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMonthlyReadingBinding
    private lateinit var adapter: MonthlyMeterAdapter
    private var selectedMonth: Int = 0 // 选择的月份 (1-12)
    private var selectedYear: Int = 0 // 选择的年份

    // 用于接收抄表结果的launcher
    private val meterReadingLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data = result.data
            val parentType = data?.getStringExtra("parentType")
            val subTypeName = data?.getStringExtra("subTypeName")

            if (parentType != null && subTypeName != null) {
                // 标记为已提交
                adapter.markAsSubmitted(parentType, subTypeName)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMonthlyReadingBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 设置标题栏返回按钮
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // 初始化月份选择
        initializeMonth()

        // 延迟显示月份选择对话框，确保Activity完全加载
        binding.root.post {
            showMonthSelectionDialog()
        }
    }

    /**
     * 初始化月份选择
     * 根据当前日期智能判断抄表月份
     */
    private fun initializeMonth() {
        val calendar = Calendar.getInstance()
        val currentDay = calendar.get(Calendar.DAY_OF_MONTH)
        val currentMonth = calendar.get(Calendar.MONTH) + 1 // Calendar.MONTH 是0-11
        val currentYear = calendar.get(Calendar.YEAR)

        // 根据日期判断抄表月份
        // 每月27日到下月5日之间都算本月的抄表
        if (currentDay >= 27) {
            // 27日及以后，算本月抄表
            selectedMonth = currentMonth
            selectedYear = currentYear
        } else if (currentDay <= 5) {
            // 5日及以前，算上月抄表
            if (currentMonth == 1) {
                selectedMonth = 12
                selectedYear = currentYear - 1
            } else {
                selectedMonth = currentMonth - 1
                selectedYear = currentYear
            }
        } else {
            // 其他时间，默认本月
            selectedMonth = currentMonth
            selectedYear = currentYear
        }
    }

    /**
     * 显示月份选择对话框
     */
    private fun showMonthSelectionDialog() {
        val monthPickerDialog = MonthPickerDialog(this) { selectedMonthValue ->
            selectedMonth = selectedMonthValue
            updateTitle()

            // 设置RecyclerView
            setupRecyclerView()

            // 加载数据
            loadMonthlyMetersWithSubTypes()
        }

        // 显示对话框，传入当前选择的月份和智能推荐月份
        monthPickerDialog.show(selectedMonth, getSmartRecommendedMonth())
    }

    /**
     * 获取智能推荐月份
     * 智能逻辑：27号之后推荐下个月，否则推荐当前月
     */
    private fun getSmartRecommendedMonth(): Int {
        val calendar = Calendar.getInstance()
        val currentDay = calendar.get(Calendar.DAY_OF_MONTH)
        val currentMonth = calendar.get(Calendar.MONTH) + 1

        return if (currentDay >= 27) {
            // 27号之后推荐下个月
            if (currentMonth == 12) 1 else currentMonth + 1
        } else {
            // 27号之前推荐当前月
            currentMonth
        }
    }

    /**
     * 更新标题显示选择的月份
     */
    private fun updateTitle() {
        supportActionBar?.title = "${selectedYear}年${selectedMonth}月抄表"
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = MonthlyMeterAdapter(
            onSubTypeClick = { parentType, subTypeName ->
                // 点击子模块跳转到填写读数界面
                val intent = Intent(this, MeterReadingActivity::class.java).apply {
                    // 将父类型和子类型一起传递，使用"/"分隔
                    putExtra(MeterReadingActivity.EXTRA_METER_TYPE, "$parentType/$subTypeName")
                    putExtra(MeterReadingActivity.EXTRA_IS_DAILY, false)
                    putExtra(MeterReadingActivity.EXTRA_MONTH, selectedMonth) // 传递选择的月份
                    putExtra("parentType", parentType)
                    putExtra("subTypeName", subTypeName)
                }
                meterReadingLauncher.launch(intent)
            },
            onScrollToPosition = { position ->
                // 滚动到指定位置的顶部
                val layoutManager = binding.rvMonthlyMeters.layoutManager as LinearLayoutManager
                layoutManager.scrollToPositionWithOffset(position, 0)
            }
        )

        binding.rvMonthlyMeters.apply {
            layoutManager = LinearLayoutManager(this@MonthlyReadingActivity)
            adapter = <EMAIL>
            // 启用嵌套滚动，让RecyclerView正常处理滑动
            isNestedScrollingEnabled = true
        }
    }

    /**
     * 加载月抄表类型及子模块
     */
    private fun loadMonthlyMetersWithSubTypes() {
        binding.progressBar.visibility = View.VISIBLE

        ApiClient.apiService.getMonthlyMeterTypesWithSubTypes().enqueue(object : Callback<ResponseBody> {
            override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                binding.progressBar.visibility = View.GONE

                if (response.isSuccessful) {
                    try {
                        val body = response.body()?.string() ?: ""
                        
                        val meterTypes = parseMeterTypes(body)
                        
                        // 更新UI
                        updateMeterTypesList(meterTypes)
                    } catch (e: Exception) {
                        Toast.makeText(
                            this@MonthlyReadingActivity,
                            "解析数据失败: ${e.message}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                } else {
                    Toast.makeText(
                        this@MonthlyReadingActivity,
                        "获取水表类型失败: ${response.code()} - ${response.message()}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
            
            override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                binding.progressBar.visibility = View.GONE
                
                Toast.makeText(
                    this@MonthlyReadingActivity,
                    "网络错误: ${t.message}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        })
    }

    /**
     * 解析月抄表类型数据
     */
    private fun parseMeterTypes(body: String): List<MonthlyMeterType> {
        val gson = Gson()

        try {
            // 尝试解析为标准API响应格式
            val apiResponse = gson.fromJson(body, ApiResponse::class.java)

            if (apiResponse.code == 0 && apiResponse.data != null) {
                // 解析data字段为月抄表类型列表
                val dataJson = gson.toJson(apiResponse.data)

                val type = object : TypeToken<List<MonthlyMeterType>>() {}.type
                val result = gson.fromJson<List<MonthlyMeterType>>(dataJson, type)
                return result
            }
        } catch (e: Exception) {
            // 备用解析：尝试直接解析为月抄表类型数组
            val trimmedBody = body.trim()

            if (trimmedBody.startsWith("[") && trimmedBody.endsWith("]")) {
                // JSON数组格式
                val type = object : TypeToken<List<MonthlyMeterType>>() {}.type
                try {
                    val result = gson.fromJson<List<MonthlyMeterType>>(trimmedBody, type)
                    return result
                } catch (e2: Exception) {
                    // 尝试解析为简单字符串列表，然后转换为MonthlyMeterType对象
                    val stringType = object : TypeToken<List<String>>() {}.type
                    val stringList = gson.fromJson<List<String>>(trimmedBody, stringType)
                    return stringList.map { MonthlyMeterType(it, emptyList(), false) }
                }
            } else {
                // 简单字符串格式，按分隔符拆分
                val result = trimmedBody.split(",", ";", "\n")
                    .map { it.trim().replace("\"", "") }
                    .filter { it.isNotEmpty() }
                    .map { MonthlyMeterType(it, emptyList(), false) }
                return result
            }
        }

        return emptyList()
    }

    /**
     * 更新月抄表类型列表
     */
    private fun updateMeterTypesList(meterTypes: List<MonthlyMeterType>) {
        if (meterTypes.isNotEmpty()) {
            adapter.updateData(meterTypes)
            // 自动提交无表项
            autoSubmitNoMeterItems(meterTypes)
        } else {
            Toast.makeText(
                this,
                "没有找到月抄表数据",
                Toast.LENGTH_SHORT
            ).show()
        }
    }
    
    /**
     * 自动提交无表项
     */
    private fun autoSubmitNoMeterItems(meterTypes: List<MonthlyMeterType>) {
        meterTypes.forEach { meterType ->
            meterType.subTypes.forEach { subType ->
                if (subType.hasNoMeter) {
                    // 自动提交无表项
                    submitNoMeterItem(meterType.parentType, subType.name)
                }
            }
        }
    }

    /**
     * 提交无表项读数
     */
    private fun submitNoMeterItem(parentType: String, subTypeName: String) {
        val meterReading = MeterReading(
            meterType = "$parentType/$subTypeName",
            readingValue = 0.0,
            previousReading = 0.0,
            photoPath = null, // 无表项不需要照片
            readingDate = Date(),
            isDaily = false,
            month = selectedMonth // 传递选择的月份
        )

        // 创建一个空的照片部分
        val emptyPart = MultipartBody.Part.createFormData("photo", "")

        ApiClient.apiService.uploadReading(meterReading, emptyPart).enqueue(object : Callback<ResponseBody> {
            override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                if (response.isSuccessful) {
                    // 成功提交，不需要特别处理
                } else {
                    // 提交失败，但不显示错误消息，因为这是自动操作
                }
            }
            
            override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                // 网络错误，但不显示错误消息，因为这是自动操作
            }
        })
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
