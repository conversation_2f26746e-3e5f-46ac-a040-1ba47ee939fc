package com.example.watermeterapp.data.api

import com.example.watermeterapp.data.model.ApiResponse
import com.example.watermeterapp.data.model.MeterReading
import com.example.watermeterapp.data.model.User
import com.example.watermeterapp.data.model.MonthlyMeterType
import com.example.watermeterapp.data.model.VersionCheckResponse
import okhttp3.MultipartBody
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.http.*

/**
 * API服务接口
 */
interface ApiService {
    
    /**
     * 登录接口 - 客户端专用
     */
    @Headers(
        "Accept: application/json",
        "User-Agent: WaterMeterApp/1.0 (Mobile)",
        "X-Requested-With: XMLHttpRequest"
    )
    @GET("auth/login")
    fun login(@Query("phone") phoneNumber: String): Call<ApiResponse<User>>
    
    /**
     * 获取上一次读数
     */
    @GET("meters/last")
    fun getLastReading(
        @Query("meterType") meterType: String, 
        @Query("isDaily") isDaily: Boolean
    ): Call<MeterReading>
    
    /**
     * 上传读数和照片
     */
    @Multipart
    @POST("meters/upload")
    fun uploadReading(
        @Part("reading") reading: MeterReading,
        @Part photo: MultipartBody.Part
    ): Call<ResponseBody>
    
    /**
     * 获取所有日抄表类型
     */
    @GET("meters/types/daily")
    fun getDailyMeterTypes(): Call<ResponseBody>
    
    /**
     * 获取所有月抄表类型
     */
    @GET("meters/types/monthly")
    fun getMonthlyMeterTypes(): Call<List<String>>
    
    /**
     * 获取月抄表类型及子模块
     */
    @GET("meters/types/monthly-with-subtypes")
    fun getMonthlyMeterTypesWithSubTypes(): Call<ResponseBody>
    
    /**
     * 获取特定月抄表类型的子模块
     */
    @GET("meters/types/monthly-subtypes")
    fun getMonthlySubTypes(@Query("parentType") parentType: String): Call<MonthlyMeterType>

    /**
     * 检查应用版本更新
     */
    @GET("app/version/check")
    fun checkVersion(
        @Query("currentVersion") currentVersion: Int,
        @Query("platform") platform: String = "android"
    ): Call<VersionCheckResponse>
}
