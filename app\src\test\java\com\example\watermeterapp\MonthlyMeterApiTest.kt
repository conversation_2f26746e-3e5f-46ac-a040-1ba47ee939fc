package com.example.watermeterapp

import com.example.watermeterapp.data.api.ApiClient
import com.example.watermeterapp.data.model.ApiResponse
import com.example.watermeterapp.data.model.MonthlyMeterType
import com.example.watermeterapp.data.model.User
import okhttp3.OkHttpClient
import okhttp3.ResponseBody
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.MockitoJUnitRunner
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.CountDownLatch

/**
 * 模拟访问服务器月度抄表功能的测试类
 */
@RunWith(MockitoJUnitRunner::class)
class MonthlyMeterApiTest {

    // 服务器地址和用户信息
    private val SERVER_URL = "http://223.76.187.251:3002/api/"
    private val USER_PHONE = "13995944332"
    
    // 模拟的API服务
    @Mock
    private lateinit var mockApiService: com.example.watermeterapp.data.api.ApiService
    
    // 模拟的API响应
    @Mock
    private lateinit var mockUserCall: Call<ApiResponse<User>>
    
    @Mock
    private lateinit var mockMonthlyTypesCall: Call<ResponseBody>
    
    @Before
    fun setup() {
        // 配置模拟的登录响应
        val user = User(
            phoneNumber = USER_PHONE,
            name = "邱东方",
            isAuthorized = true,
            department = "物业部",
            role = "系统管理员",
            token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MjIsInBob25lIjoiMTM5OTU5NDQzMzIiLCJuYW1lIjoi6YKx5Lic5pa5IiwiYXV0aExldmVsIjoxLCJpYXQiOjE3NTExMzA2NjQsImV4cCI6MTc1MTIxNzA2NH0.TMK_XfMmlyjdstJQ69vGN5MlK9BbRzMRBQMekeeITFI",
            authLevel = 1
        )
        val loginResponse = ApiResponse(0, "登录成功", user)
        
        // 配置模拟的月度抄表类型响应
        val monthlyTypes = listOf(
            MonthlyMeterType("隔油间", listOf(
                MonthlyMeterType.SubMeterType("1#隔油间"),
                MonthlyMeterType.SubMeterType("2#隔油间"),
                MonthlyMeterType.SubMeterType("3#隔油间", hasNoMeter = true)
            )),
            MonthlyMeterType("生活水泵房", listOf(
                MonthlyMeterType.SubMeterType("1#水泵"),
                MonthlyMeterType.SubMeterType("2#水泵")
            )),
            MonthlyMeterType("中水机房", listOf(
                MonthlyMeterType.SubMeterType("进水总表"),
                MonthlyMeterType.SubMeterType("出水总表")
            ))
        )
        
        // 配置模拟的API调用
        `when`(mockApiService.login(USER_PHONE)).thenReturn(mockUserCall)
        `when`(mockApiService.getMonthlyMeterTypesWithSubTypes()).thenReturn(mockMonthlyTypesCall)
        
        // 配置模拟的API响应回调
        setupMockCallResponse(mockUserCall, Response.success(loginResponse))
    }
    
    /**
     * 测试登录功能
     */
    @Test
    fun testLogin() {
        println("===== 模拟登录测试 =====")
        println("服务器地址: $SERVER_URL")
        println("用户手机号: $USER_PHONE")
        
        val latch = CountDownLatch(1)
        
        mockApiService.login(USER_PHONE).enqueue(object : Callback<ApiResponse<User>> {
            override fun onResponse(call: Call<ApiResponse<User>>, response: Response<ApiResponse<User>>) {
                if (response.isSuccessful) {
                    val user = response.body()?.data
                    println("登录成功！")
                    println("用户信息: ${user?.name} (${user?.phoneNumber})")
                    println("部门: ${user?.department}")
                    println("角色: ${user?.role}")
                    println("Token: ${user?.token?.take(20)}...")
                } else {
                    println("登录失败: ${response.code()} - ${response.message()}")
                }
                latch.countDown()
            }
            
            override fun onFailure(call: Call<ApiResponse<User>>, t: Throwable) {
                println("网络错误: ${t.message}")
                latch.countDown()
            }
        })
        
        latch.await()
    }
    
    /**
     * 测试获取月度抄表类型
     */
    @Test
    fun testGetMonthlyMeterTypes() {
        println("===== 模拟获取月度抄表类型测试 =====")
        
        val monthlyTypes = listOf(
            MonthlyMeterType("隔油间", listOf(
                MonthlyMeterType.SubMeterType("1#隔油间"),
                MonthlyMeterType.SubMeterType("2#隔油间"),
                MonthlyMeterType.SubMeterType("3#隔油间", hasNoMeter = true)
            )),
            MonthlyMeterType("生活水泵房", listOf(
                MonthlyMeterType.SubMeterType("1#水泵"),
                MonthlyMeterType.SubMeterType("2#水泵")
            )),
            MonthlyMeterType("中水机房", listOf(
                MonthlyMeterType.SubMeterType("进水总表"),
                MonthlyMeterType.SubMeterType("出水总表")
            ))
        )
        
        println("获取到${monthlyTypes.size}个月度抄表类型:")
        monthlyTypes.forEachIndexed { index, type ->
            println("${index + 1}. ${type.parentType} (${type.subTypes.size}个子类型)")
            type.subTypes.forEachIndexed { subIndex, subType ->
                val noMeterMark = if (subType.hasNoMeter) "[无表]" else ""
                println("   ${subIndex + 1}. ${subType.name} $noMeterMark")
            }
        }
    }
    
    /**
     * 配置模拟的API响应回调
     */
    private fun <T> setupMockCallResponse(call: Call<T>, response: Response<T>) {
        `when`(call.enqueue(org.mockito.ArgumentMatchers.any())).thenAnswer { invocation ->
            val callback = invocation.getArgument<Callback<T>>(0)
            callback.onResponse(call, response)
            null
        }
    }
} 