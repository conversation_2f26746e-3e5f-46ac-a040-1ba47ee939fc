const db = require('./db');

/**
 * 安全的数据库迁移脚本 - 只添加缺失的字段，不影响现有数据
 */
async function migrateAuthFields() {
    try {
        console.log('开始检查和迁移认证相关字段...');

        // 检查QuanXianJiBie字段是否存在
        const [columns] = await db.execute(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'watermeter' 
            AND TABLE_NAME = 'YongHu' 
            AND COLUMN_NAME = 'QuanXianJiBie'
        `);

        if (columns.length === 0) {
            console.log('添加权限级别字段 QuanXianJiBie...');
            await db.execute(`
                ALTER TABLE YongHu 
                ADD COLUMN QuanXianJiBie INT DEFAULT 1 COMMENT '权限级别: 1=完整权限, 2=仅客户端, 3=部分前端权限'
            `);
            console.log('✅ 权限级别字段添加成功');
        } else {
            console.log('✅ 权限级别字段已存在');
        }

        // 检查GengXinShiJian字段是否存在
        const [updateTimeColumns] = await db.execute(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = 'watermeter' 
            AND TABLE_NAME = 'YongHu' 
            AND COLUMN_NAME = 'GengXinShiJian'
        `);

        if (updateTimeColumns.length === 0) {
            console.log('添加更新时间字段 GengXinShiJian...');
            await db.execute(`
                ALTER TABLE YongHu 
                ADD COLUMN GengXinShiJian TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
            `);
            console.log('✅ 更新时间字段添加成功');
        } else {
            console.log('✅ 更新时间字段已存在');
        }

        // 为现有用户设置默认权限级别（如果为NULL）
        console.log('检查并设置现有用户的默认权限级别...');
        const [result] = await db.execute(`
            UPDATE YongHu 
            SET QuanXianJiBie = 1 
            WHERE QuanXianJiBie IS NULL
        `);
        
        if (result.affectedRows > 0) {
            console.log(`✅ 为 ${result.affectedRows} 个用户设置了默认权限级别`);
        } else {
            console.log('✅ 所有用户都已有权限级别设置');
        }

        // 检查是否有超级管理员账号
        const [adminUsers] = await db.execute(`
            SELECT * FROM YongHu 
            WHERE ShouJiHao IN ('18888888888', '13800138000') 
            AND QuanXianJiBie = 1
        `);

        if (adminUsers.length === 0) {
            console.log('建议：您可以手动将某个用户的权限级别设置为1（完整权限）以便管理系统');
            console.log('SQL示例：UPDATE YongHu SET QuanXianJiBie = 1 WHERE ShouJiHao = "您的手机号"');
        } else {
            console.log(`✅ 找到 ${adminUsers.length} 个管理员账号`);
        }

        console.log('\n🎉 数据库迁移完成！');
        console.log('\n权限级别说明：');
        console.log('1 = 完整权限（可访问所有功能）');
        console.log('2 = 仅客户端权限（只能使用移动端）');
        console.log('3 = 部分前端权限（只能查看日抄表和月抄表）');

    } catch (error) {
        console.error('❌ 数据库迁移失败:', error.message);
        throw error;
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    migrateAuthFields()
        .then(() => {
            console.log('\n迁移脚本执行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('迁移脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = { migrateAuthFields };
