package com.example.watermeterapp

/**
 * 月度抄表API响应数据更新
 * 
 * 根据图片中显示的实际数据更新月度抄表类型
 */
object MonthlyMeterApiResponseUpdate {

    /**
     * 获取所有月抄表类型 (getMonthlyMeterTypes)
     * GET /api/meters/types/monthly
     * 
     * 响应示例 (根据图片更新):
     * ["隔油间", "空调机房", "卫生间", "其它用水", "商铺", "T1", "T2"]
     */
    val allMonthlyMeterTypesResponse = """
    ["隔油间", "空调机房", "卫生间", "其它用水", "商铺", "T1", "T2"]
    """.trimIndent()
    
    /**
     * 获取月抄表类型及子模块 (getMonthlyMeterTypesWithSubTypes)
     * GET /api/meters/types/monthly-with-subtypes
     * 
     * 响应示例 (根据图片更新):
     */
    val monthlyMeterTypesWithSubTypesResponse = """
    {
        "code": 0,
        "msg": "获取成功",
        "data": [
            {
                "parentType": "隔油间",
                "subTypes": [
                    {
                        "name": "1#隔油间",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#隔油间",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "3#隔油间",
                        "hasNoMeter": true,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "4#隔油间",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "5#隔油间",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false,
                "count": 18
            },
            {
                "parentType": "空调机房",
                "subTypes": [
                    {
                        "name": "1#空调机房",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#空调机房",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "3#空调机房",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false,
                "count": 32
            },
            {
                "parentType": "卫生间",
                "subTypes": [
                    {
                        "name": "1#卫生间",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#卫生间",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "3#卫生间",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false,
                "count": 15
            },
            {
                "parentType": "其它用水",
                "subTypes": [
                    {
                        "name": "1#其它用水",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#其它用水",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false,
                "count": 10
            },
            {
                "parentType": "商铺",
                "subTypes": [
                    {
                        "name": "1#商铺",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#商铺",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false,
                "count": 0
            },
            {
                "parentType": "T1",
                "subTypes": [
                    {
                        "name": "1#T1",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#T1",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false,
                "count": 7
            },
            {
                "parentType": "T2",
                "subTypes": [
                    {
                        "name": "1#T2",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#T2",
                        "hasNoMeter": false,
                        "isSubmitted": true,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false,
                "count": 6
            }
        ]
    }
    """.trimIndent()
    
    /**
     * 月度抄表数据结构总结（根据图片更新）
     */
    fun getSummary(): String {
        val sb = StringBuilder()
        
        sb.appendLine("===== 月度抄表API响应数据总结（更新版）=====")
        sb.appendLine()
        
        sb.appendLine("服务器返回的月度抄表类型共有7个:")
        sb.appendLine("1. 隔油间 (计数: 18)")
        sb.appendLine("2. 空调机房 (计数: 32)")
        sb.appendLine("3. 卫生间 (计数: 15)")
        sb.appendLine("4. 其它用水 (计数: 10)")
        sb.appendLine("5. 商铺 (计数: 0)")
        sb.appendLine("6. T1 (计数: 7)")
        sb.appendLine("7. T2 (计数: 6)")
        
        return sb.toString()
    }
    
    /**
     * 打印所有API响应数据
     */
    fun printAllApiResponses(): String {
        val sb = StringBuilder()
        
        // 1. 获取所有月抄表类型API响应
        sb.appendLine("===== 获取所有月抄表类型API响应 =====")
        sb.appendLine("请求: GET /api/meters/types/monthly")
        sb.appendLine("响应:")
        sb.appendLine(allMonthlyMeterTypesResponse)
        sb.appendLine()
        
        // 2. 月度抄表类型API响应
        sb.appendLine("===== 获取月抄表类型及子模块API响应 =====")
        sb.appendLine("请求: GET /api/meters/types/monthly-with-subtypes")
        sb.appendLine("响应:")
        sb.appendLine(monthlyMeterTypesWithSubTypesResponse)
        sb.appendLine()
        
        // 3. 格式化打印月度抄表数据
        sb.appendLine(getSummary())
        
        return sb.toString()
    }
}

/**
 * 主函数，用于直接运行并打印所有API响应数据
 */
fun main() {
    println("开始模拟访问服务器月度抄表数据...")
    println("服务器地址: http://223.76.187.251:3002/api/")
    println("用户手机号: 13995944332")
    println()
    
    // 打印所有API响应数据
    println(MonthlyMeterApiResponseUpdate.printAllApiResponses())
    
    println("模拟访问完成")
} 