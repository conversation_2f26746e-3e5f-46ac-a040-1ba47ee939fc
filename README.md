# 水表抄录系统 Android 应用

## 项目概述

水表抄录系统是一款专为水务管理部门设计的移动端应用，旨在数字化和标准化水表读数采集流程。该应用支持日常抄表和月度抄表两种工作模式，通过手机拍照和数据录入的方式，实现水表读数的快速采集、本地存储和云端同步。

## 主要功能

### 🔐 用户认证系统
- **手机号登录**：支持11位手机号码验证登录
- **权限验证**：后端验证用户是否具有抄表权限
- **测试账号**：提供三个测试账号便于开发和演示
- **用户信息展示**：显示用户姓名、部门、角色等信息

### 🎉 欢迎界面
- **用户信息卡片**：展示登录用户的详细信息
- **自动跳转**：2秒后自动进入主界面
- **手动进入**：支持点击按钮立即进入系统

### 🏠 主界面
- **用户信息栏**：顶部显示当前登录用户信息
- **功能导航**：提供日抄表和月抄表两个主要功能入口
- **现代化设计**：采用卡片式布局，配色专业

### 📊 抄表功能

#### 日抄表模块
- **表类型管理**：支持多种日抄表类型
  - 生活水泵房
  - 中水机房  
  - 室外消防总表
  - 室外生活总表
- **读数录入**：数字键盘输入当前读数
- **历史对比**：显示上次读数作为参考
- **照片采集**：必须拍摄水表照片作为凭证

#### 月抄表模块
- **表类型管理**：支持多种月抄表类型
  - 隔油机房
  - 空调机房
  - 卫生间
  - 其他机房
- **功能特性**：与日抄表相同的录入和拍照流程

### 📷 拍照功能
- **相机权限管理**：自动申请和检查相机权限
- **照片预览**：拍摄后可预览照片
- **本地存储**：照片保存到应用私有目录
- **云端上传**：与读数一起上传到服务器

### 💾 数据管理
- **本地缓存**：支持离线模式下的数据录入
- **数据同步**：网络恢复后自动同步到服务器
- **数据验证**：确保读数的合理性和完整性

## 技术架构

### 前端技术栈
- **开发语言**：Kotlin
- **UI框架**：Android Jetpack + Material Design
- **架构模式**：MVVM
- **网络请求**：Retrofit2 + OkHttp3
- **图片处理**：Android Camera API
- **权限管理**：EasyPermissions
- **数据绑定**：View Binding

### 项目结构
```
app/src/main/java/com/example/watermeterapp/
├── data/                    # 数据层
│   ├── api/                # API接口和网络请求
│   └── model/              # 数据模型
├── ui/                     # UI层
│   ├── login/              # 登录界面
│   ├── welcome/            # 欢迎界面
│   ├── main/               # 主界面
│   ├── daily/              # 日抄表界面
│   ├── monthly/            # 月抄表界面
│   └── reading/            # 读数录入界面
└── utils/                  # 工具类
    ├── ImageUtils.kt       # 图片处理工具
    └── PermissionUtils.kt  # 权限管理工具
```

## 功能模块详细说明

### 1. 登录模块 (LoginActivity)
**功能描述**：用户身份验证和权限检查

**主要特性**：
- 手机号格式验证（11位数字）
- 网络请求验证用户权限
- 测试账号快速登录
- 错误提示和加载状态

**业务流程**：
1. 用户输入手机号或点击测试账号
2. 前端验证手机号格式
3. 调用后端API验证用户权限
4. 验证成功跳转到欢迎界面

### 2. 欢迎模块 (WelcomeActivity)
**功能描述**：展示用户信息，提供友好的进入体验

**主要特性**：
- 用户信息卡片展示
- 渐变背景设计
- 自动跳转机制
- 过渡动画效果

### 3. 主界面模块 (MainActivity)
**功能描述**：系统功能导航中心

**主要特性**：
- 用户信息展示
- 功能模块导航
- 权限申请管理
- 现代化UI设计

### 4. 抄表列表模块 (DailyReadingActivity / MonthlyReadingActivity)
**功能描述**：展示可抄读的水表类型列表

**主要特性**：
- 动态加载表类型
- 列表展示和点击跳转
- 加载状态提示
- 错误处理机制

### 5. 读数录入模块 (MeterReadingActivity)
**功能描述**：核心业务功能，录入水表读数和拍照

**主要特性**：
- 显示表类型和上次读数
- 数字输入验证
- 相机拍照功能
- 数据提交和上传

**业务流程**：
1. 显示水表基本信息
2. 获取并显示上次读数
3. 用户输入当前读数
4. 拍摄水表照片
5. 验证数据完整性
6. 提交到服务器

## 后端服务需求

### API接口规范

#### 1. 用户验证接口
```
GET /api/auth/verify?phone={phoneNumber}
```
**功能**：验证用户手机号并返回用户信息
**请求参数**：
- phone: 用户手机号（11位数字）

**响应格式**：
```json
{
  "phoneNumber": "13800138000",
  "name": "张三",
  "isAuthorized": true,
  "department": "水务管理部",
  "role": "抄表员"
}
```

#### 2. 获取上次读数接口
```
GET /api/meters/last?meterType={type}&isDaily={boolean}
```
**功能**：获取指定水表的最近一次读数
**请求参数**：
- meterType: 水表类型
- isDaily: 是否为日抄表

**响应格式**：
```json
{
  "id": 1,
  "meterType": "生活水泵房",
  "readingValue": 1234.56,
  "previousReading": 1200.00,
  "readingDate": "2024-01-15T10:30:00Z",
  "isDaily": true
}
```

#### 3. 上传读数接口
```
POST /api/meters/upload
Content-Type: multipart/form-data
```
**功能**：上传水表读数和照片
**请求参数**：
- reading: JSON格式的读数数据
- photo: 水表照片文件

**响应格式**：
```json
{
  "success": true,
  "message": "上传成功",
  "id": 12345
}
```

#### 4. 获取表类型接口
```
GET /api/meters/types/daily    # 获取日抄表类型
GET /api/meters/types/monthly  # 获取月抄表类型
```
**功能**：获取可用的水表类型列表
**响应格式**：
```json
[
  "生活水泵房",
  "中水机房",
  "室外消防总表",
  "室外生活总表"
]
```

### 数据库设计建议

#### 用户表 (users)
```sql
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  phone_number VARCHAR(11) UNIQUE NOT NULL,
  name VARCHAR(50) NOT NULL,
  department VARCHAR(100),
  role VARCHAR(50),
  is_authorized BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 水表类型表 (meter_types)
```sql
CREATE TABLE meter_types (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  type_name VARCHAR(100) NOT NULL,
  is_daily BOOLEAN NOT NULL,
  is_active BOOLEAN DEFAULT TRUE
);
```

#### 读数记录表 (meter_readings)
```sql
CREATE TABLE meter_readings (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_phone VARCHAR(11) NOT NULL,
  meter_type VARCHAR(100) NOT NULL,
  reading_value DECIMAL(10,2) NOT NULL,
  previous_reading DECIMAL(10,2),
  photo_url VARCHAR(500),
  reading_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_daily BOOLEAN NOT NULL,
  is_uploaded BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 服务器环境要求

#### 基础环境
- **操作系统**：Linux (Ubuntu 20.04+ 推荐)
- **Web服务器**：Nginx
- **应用服务器**：Node.js / Java Spring Boot / Python Django
- **数据库**：MySQL 8.0+ 或 PostgreSQL 12+
- **文件存储**：本地存储或云存储服务

#### 性能要求
- **并发用户**：支持100+并发用户
- **响应时间**：API响应时间 < 2秒
- **文件上传**：支持10MB以内的图片上传
- **数据备份**：每日自动备份数据库

#### 安全要求
- **HTTPS**：强制使用SSL证书
- **API认证**：Token或Session验证
- **数据加密**：敏感数据加密存储
- **访问日志**：记录所有API访问日志

## 部署说明

### Android应用部署
1. 使用Android Studio编译生成APK文件
2. 通过应用商店或企业分发平台发布
3. 支持Android 7.0 (API 24) 及以上版本

### 后端服务部署
1. 配置服务器环境和数据库
2. 部署API服务和文件存储
3. 配置域名和SSL证书
4. 设置监控和日志系统

## 测试账号

应用内置三个测试账号，便于开发和演示：

| 姓名 | 手机号 | 部门 | 角色 |
|------|--------|------|------|
| 张三 | 13800138000 | 水务管理部 | 抄表员 |
| 李四 | 13900139000 | 设备维护部 | 抄表员 |
| 王五 | 13700137000 | 运营管理部 | 抄表员 |

## 联系信息

如有技术问题或需求变更，请联系开发团队。
