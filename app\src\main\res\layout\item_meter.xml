<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="16dp"
    android:layout_marginRight="16dp"
    android:layout_marginTop="8dp"
    android:layout_marginBottom="8dp"
    app:cardBackgroundColor="@color/white"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:id="@+id/meterLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <TextView
            android:id="@+id/tvMeterName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#333333"
            android:textSize="16sp"
            android:gravity="start"
            android:layout_marginBottom="12dp" />

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#E0E0E0"
            android:layout_marginBottom="16dp" />

        <!-- 隐藏的TextView，保持代码兼容性 -->
        <TextView
            android:id="@+id/tvLastReading"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvLastDate"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnFillReading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="填写读数"
            android:textColor="@color/white"
            android:backgroundTint="#2196F3"
            style="@style/Widget.MaterialComponents.Button" />

    </LinearLayout>

</androidx.cardview.widget.CardView>