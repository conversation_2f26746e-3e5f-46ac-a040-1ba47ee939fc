package com.example.watermeterapp.data.api.adapter

import com.example.watermeterapp.data.model.User
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.google.gson.JsonParseException
import java.lang.reflect.Type

/**
 * User对象的自定义反序列化器
 * 能够处理字符串和JSON对象两种响应格式
 */
class UserDeserializer : JsonDeserializer<User> {

    override fun deserialize(
        json: JsonElement, 
        typeOfT: Type, 
        context: JsonDeserializationContext
    ): User {
        try {
            // 如果是字符串，尝试提取信息
            if (json.isJsonPrimitive && json.asJsonPrimitive.isString) {
                val responseStr = json.asString
                
                // 根据字符串内容创建用户对象
                // 之前的判断逻辑存在问题，可能错误解析
                // 明确检测"denied"字样优先级高于检测"authorized"
                val isAuthorized = if (responseStr.contains("denied", ignoreCase = true) || 
                                       responseStr.contains("unauthorized", ignoreCase = true) ||
                                       responseStr.contains("not authorized", ignoreCase = true)) {
                    false
                } else if (responseStr.contains("authorized", ignoreCase = true) || 
                          responseStr.contains("success", ignoreCase = true)) {
                    true
                } else {
                    // 如果没有明确的授权或未授权关键字，默认为未授权
                    false
                }
                
                // 从字符串中尝试提取电话号码
                val phoneRegex = "\\d{11}".toRegex()
                val phoneMatch = phoneRegex.find(responseStr)
                val phone = phoneMatch?.value ?: ""
                
                // 创建用户对象
                return User(
                    phoneNumber = phone,
                    name = "用户_$phone",
                    isAuthorized = isAuthorized
                )
            }
            // 如果是JSON对象，正常解析
            else if (json.isJsonObject) {
                val jsonObject = json.asJsonObject
                
                val phoneNumber = getStringOrDefault(jsonObject, "phoneNumber", "")
                val name = getStringOrDefault(jsonObject, "name", "")
                val department = getStringOrDefault(jsonObject, "department", "")
                val role = getStringOrDefault(jsonObject, "role", "抄表员")
                
                // 处理isAuthorized字段，可能是布尔值或数字
                val isAuthorized = if (jsonObject.has("isAuthorized")) {
                    val element = jsonObject.get("isAuthorized")
                    val authValue = when {
                        element.isJsonPrimitive && element.asJsonPrimitive.isBoolean -> element.asBoolean
                        element.isJsonPrimitive && element.asJsonPrimitive.isNumber -> element.asInt != 0
                        element.isJsonPrimitive && element.asJsonPrimitive.isString -> {
                            val value = element.asString
                            value.equals("true", ignoreCase = true) || value == "1"
                        }
                        else -> false
                    }
                    authValue
                } else {
                    false
                }
                
                val user = User(phoneNumber, name, isAuthorized, department, role)
                return user
            }
            // 其他情况，返回空用户对象
            else {
                return User("", "", false)
            }
        } catch (e: Exception) {
            throw JsonParseException("解析用户数据失败", e)
        }
    }
    
    /**
     * 从JsonObject中获取字符串，如果不存在或不是字符串则返回默认值
     */
    private fun getStringOrDefault(jsonObject: JsonObject, key: String, defaultValue: String): String {
        return if (jsonObject.has(key) && jsonObject.get(key).isJsonPrimitive && 
                  jsonObject.get(key).asJsonPrimitive.isString) {
            jsonObject.get(key).asString
        } else {
            defaultValue
        }
    }
} 