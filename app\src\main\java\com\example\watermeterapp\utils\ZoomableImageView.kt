package com.example.watermeterapp.utils

import android.content.Context
import android.graphics.Matrix
import android.graphics.PointF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import androidx.appcompat.widget.AppCompatImageView

/**
 * 支持双指缩放的ImageView
 */
class ZoomableImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private var matrix = Matrix()
    private var mode = NONE

    // 记录缩放比例
    private var savedMatrix = Matrix()
    private var start = PointF()
    private var mid = PointF()
    private var oldDist = 1f
    private var lastEvent: FloatArray? = null

    // 缩放检测器
    private var mScaleDetector: ScaleGestureDetector
    
    // 缩放相关参数
    private var minScale = 1.0f
    private var maxScale = 5.0f
    private var currentScale = 1.0f
    
    // 是否启用缩放
    var isZoomEnabled = false

    companion object {
        // 定义不同的触摸模式
        private const val NONE = 0
        private const val DRAG = 1
        private const val ZOOM = 2
    }

    init {
        scaleType = ScaleType.MATRIX
        
        // 初始化缩放检测器
        mScaleDetector = ScaleGestureDetector(context, ScaleListener())
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (!isZoomEnabled) {
            return super.onTouchEvent(event)
        }
        
        // 将事件传递给缩放检测器
        mScaleDetector.onTouchEvent(event)

        val curr = PointF(event.x, event.y)

        when (event.action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_DOWN -> {
                savedMatrix.set(matrix)
                start.set(curr)
                mode = DRAG
                lastEvent = null
            }
            MotionEvent.ACTION_POINTER_DOWN -> {
                oldDist = spacing(event)
                if (oldDist > 10f) {
                    savedMatrix.set(matrix)
                    midPoint(mid, event)
                    mode = ZOOM
                }
                lastEvent = FloatArray(4)
                lastEvent!![0] = event.getX(0)
                lastEvent!![1] = event.getX(1)
                lastEvent!![2] = event.getY(0)
                lastEvent!![3] = event.getY(1)
            }
            MotionEvent.ACTION_MOVE -> {
                if (mode == DRAG) {
                    matrix.set(savedMatrix)
                    matrix.postTranslate(event.x - start.x, event.y - start.y)
                } else if (mode == ZOOM) {
                    val newDist = spacing(event)
                    if (newDist > 10f) {
                        matrix.set(savedMatrix)
                        val scale = newDist / oldDist
                        matrix.postScale(scale, scale, mid.x, mid.y)
                    }
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_POINTER_UP -> {
                mode = NONE
            }
        }

        // 应用变换
        imageMatrix = matrix
        return true
    }

    // 计算两指间的距离
    private fun spacing(event: MotionEvent): Float {
        val x = event.getX(0) - event.getX(1)
        val y = event.getY(0) - event.getY(1)
        return Math.sqrt((x * x + y * y).toDouble()).toFloat()
    }

    // 计算两指间的中点
    private fun midPoint(point: PointF, event: MotionEvent) {
        val x = event.getX(0) + event.getX(1)
        val y = event.getY(0) + event.getY(1)
        point.set(x / 2, y / 2)
    }

    // 缩放监听器
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScale(detector: ScaleGestureDetector): Boolean {
            val scaleFactor = detector.scaleFactor
            
            // 限制缩放范围
            val newScale = currentScale * scaleFactor
            if (newScale in minScale..maxScale) {
                currentScale = newScale
                
                val focusX = detector.focusX
                val focusY = detector.focusY
                
                matrix.postScale(scaleFactor, scaleFactor, focusX, focusY)
                imageMatrix = matrix
            }
            
            return true
        }
    }

    // 重置缩放和位置
    fun resetZoom() {
        currentScale = 1.0f
        matrix.reset()
        imageMatrix = matrix
    }
} 