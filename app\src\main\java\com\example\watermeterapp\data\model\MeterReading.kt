﻿package com.example.watermeterapp.data.model

import java.util.Date

/**
 * 水表读数数据模型
 */
data class MeterReading(
    val id: Long = 0,
    val meterType: String, // 表类型，如"生活水泵房"、"中水机房"等
    val readingValue: Double, // 读数值
    val previousReading: Double = 0.0, // 上次读数（兼容性保留）
    val photoPath: String? = null, // 照片本地路径
    val photoUrl: String? = null, // 照片服务器URL
    val readingDate: Date = Date(), // 抄表日期
    val reader: String? = null, // 抄表人
    val uploadTime: Date? = null, // 上传时间
    val isUploaded: Boolean = false, // 是否已上传
    val isDaily: Boolean, // 是否为日抄表
    val month: Int? = null, // 月抄表时的月份参数(1-12)
    val userPhone: String? = null // 用户手机号
)
