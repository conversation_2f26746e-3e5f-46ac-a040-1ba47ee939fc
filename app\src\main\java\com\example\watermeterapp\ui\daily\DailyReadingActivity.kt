﻿package com.example.watermeterapp.ui.daily

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.watermeterapp.data.api.ApiClient
import com.example.watermeterapp.data.model.ApiResponse
import com.example.watermeterapp.data.model.MeterInfo
import com.example.watermeterapp.databinding.ActivityDailyReadingBinding
import com.example.watermeterapp.ui.adapter.MeterAdapter
import com.example.watermeterapp.ui.reading.MeterReadingActivity
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import okhttp3.ResponseBody
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

class DailyReadingActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDailyReadingBinding
    private lateinit var adapter: MeterAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityDailyReadingBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 设置标题栏返回按钮
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // 设置RecyclerView
        setupRecyclerView()

        // 加载数据
        loadMeterTypes()
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        adapter = MeterAdapter { meterType ->
            // 点击跳转到填写读数界面
            val intent = Intent(this, MeterReadingActivity::class.java).apply {
                putExtra(MeterReadingActivity.EXTRA_METER_TYPE, meterType)
                putExtra(MeterReadingActivity.EXTRA_IS_DAILY, true)
            }
            startActivity(intent)
        }

        binding.rvDailyMeters.apply {
            layoutManager = LinearLayoutManager(this@DailyReadingActivity)
            adapter = <EMAIL>
            // 在ScrollView中使用RecyclerView时禁用嵌套滚动
            isNestedScrollingEnabled = false
        }
    }

    /**
     * 加载日抄表类型
     */
    private fun loadMeterTypes() {
        binding.progressBar.visibility = View.VISIBLE
        
        ApiClient.apiService.getDailyMeterTypes().enqueue(object : Callback<ResponseBody> {
            override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                binding.progressBar.visibility = View.GONE
                
                if (response.isSuccessful) {
                    try {
                        val body = response.body()?.string() ?: ""
                        val meterTypes = parseMeterTypes(body)
                        
                        // 更新UI
                        updateMeterTypesList(meterTypes)
                    } catch (e: Exception) {
                        Toast.makeText(
                            this@DailyReadingActivity,
                            "解析数据失败: ${e.message}",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                } else {
                    Toast.makeText(
                        this@DailyReadingActivity,
                        "获取水表类型失败: ${response.code()} - ${response.message()}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
            
            override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                binding.progressBar.visibility = View.GONE
                
                Toast.makeText(
                    this@DailyReadingActivity,
                    "网络错误: ${t.message}",
                    Toast.LENGTH_SHORT
                ).show()
            }
        })
    }

    /**
     * 解析水表类型数据
     */
    private fun parseMeterTypes(body: String): List<String> {
        val gson = Gson()
        val listType = object : TypeToken<List<String>>() {}.type
        return gson.fromJson(body, listType)
    }

    /**
     * 更新水表类型列表
     */
    private fun updateMeterTypesList(meterTypes: List<String>) {
        // 转换为MeterInfo对象，暂时使用模拟的上次抄表数据
        val meterInfos = meterTypes.map { meterType ->
            MeterInfo(
                meterType = meterType,
                lastReading = null, // 暂时为空，后续可以从API获取
                lastReadingDate = null // 暂时为空，后续可以从API获取
            )
        }

        // 更新适配器数据
        adapter.updateData(meterInfos)

        if (meterInfos.isEmpty()) {
            Toast.makeText(this, "暂无日抄表类型", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        super.onBackPressed()
        // 可以添加自定义的返回逻辑
        overridePendingTransition(android.R.anim.slide_in_left, android.R.anim.slide_out_right)
    }
}
