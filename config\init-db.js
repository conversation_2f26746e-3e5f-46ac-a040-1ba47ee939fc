const mysql = require('mysql2/promise');

async function initializeDatabase() {
  // 创建数据库连接
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: ''
  });

  try {
    // 创建数据库
    await connection.query('CREATE DATABASE IF NOT EXISTS waterMeter CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');

    // 使用waterMeter数据库
    await connection.query('USE waterMeter');

    // 创建用户表 (users)
    await connection.query(`
      CREATE TABLE IF NOT EXISTS YongHu (
        Id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
        ShouJiHao VARCHAR(11) UNIQUE NOT NULL COMMENT '手机号',
        XingMing VARCHAR(50) NOT NULL COMMENT '姓名',
        BuMen VARCHAR(100) COMMENT '部门',
        JueSe VARCHAR(50) COMMENT '角色',
        <PERSON><PERSON><PERSON><PERSON> BOOLEAN DEFAULT FALSE COMMENT '是否授权',
        QuanXianJiBie INT DEFAULT 1 COMMENT '权限级别: 1=完整权限, 2=仅客户端, 3=部分前端权限',
        ChuangJianShiJian TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        GengXinShiJian TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
      ) COMMENT='用户表';
    `);

    // 检查并添加权限级别字段（如果表已存在但没有此字段）
    await connection.query(`
      ALTER TABLE YongHu
      ADD COLUMN IF NOT EXISTS QuanXianJiBie INT DEFAULT 1 COMMENT '权限级别: 1=完整权限, 2=仅客户端, 3=部分前端权限',
      ADD COLUMN IF NOT EXISTS GengXinShiJian TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
    `);


    // 创建水表类型表 (meter_types)
    await connection.query(`
      CREATE TABLE IF NOT EXISTS BiaoDianLeiXing (
        Id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '类型ID',
        LeiXingMingCheng VARCHAR(100) NOT NULL COMMENT '类型名称',
        RiChaoBiao BOOLEAN NOT NULL COMMENT '是否日抄表',
        YueChaoBiaoLeiXing VARCHAR(50) COMMENT '月抄表子类型',
        ShiFouQiYong BOOLEAN DEFAULT TRUE COMMENT '是否启用'
      ) COMMENT='水表类型表';
    `);


    // 创建日抄表记录表 (daily_meter_readings)
    await connection.query(`
      CREATE TABLE IF NOT EXISTS RiChaoBiaoJiLu (
        Id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
        YongHuShouJi VARCHAR(11) NOT NULL COMMENT '用户手机',
        BiaoDianLeiXing VARCHAR(100) NOT NULL COMMENT '表点类型',
        ChaoBiaoShuZhi DECIMAL(10,2) NOT NULL COMMENT '抄表数值',
        ShangCiShuZhi DECIMAL(10,2) COMMENT '上次数值',
        ZhaoPianURL VARCHAR(500) COMMENT '照片URL',
        ChaoBiaoShiJian TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '抄表时间',
        ShiFouRiChaoBiao BOOLEAN NOT NULL COMMENT '是否日抄表',
        YueChaoBiaoXiangXiId BIGINT COMMENT '月抄表详细信息ID',
        ShiFouShangChuan BOOLEAN DEFAULT TRUE COMMENT '是否上传',
        ChuangJianShiJian TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
      ) COMMENT='日抄表记录表';
    `);


    // 创建月度抄表记录表 (monthly_meter_readings)
    await connection.query(`
      CREATE TABLE IF NOT EXISTS YueDuChaoBiaoJiLu (
        Id INT AUTO_INCREMENT PRIMARY KEY,
        LeiXing VARCHAR(50) NOT NULL COMMENT '类型',
        LouCeng VARCHAR(20) COMMENT '楼层',
        BianHao VARCHAR(50) COMMENT '编号',
        MingCheng VARCHAR(100) COMMENT '名称',
        ShuiBiaoBianHao VARCHAR(50) COMMENT '水表编号',

        Dec_ZhiMa DECIMAL(10,2) DEFAULT 0 COMMENT '12月止码',
        Dec_YongLiang DECIMAL(10,2) DEFAULT 0 COMMENT '12月用量',
        Dec_ShuiFei DECIMAL(10,2) DEFAULT 0 COMMENT '12月水费',

        Jan_ZhiMa DECIMAL(10,2) DEFAULT 0 COMMENT '1月止码',
        Jan_YongLiang DECIMAL(10,2) DEFAULT 0 COMMENT '1月用量',
        Jan_ShuiFei DECIMAL(10,2) DEFAULT 0 COMMENT '1月水费',

        Feb_ZhiMa DECIMAL(10,2) DEFAULT 0 COMMENT '2月止码',
        Feb_YongLiang DECIMAL(10,2) DEFAULT 0 COMMENT '2月用量',
        Feb_ShuiFei DECIMAL(10,2) DEFAULT 0 COMMENT '2月水费',

        Mar_ZhiMa DECIMAL(10,2) DEFAULT 0 COMMENT '3月止码',
        Mar_YongLiang DECIMAL(10,2) DEFAULT 0 COMMENT '3月用量',
        Mar_ShuiFei DECIMAL(10,2) DEFAULT 0 COMMENT '3月水费',

        Apr_ZhiMa DECIMAL(10,2) DEFAULT 0 COMMENT '4月止码',
        Apr_YongLiang DECIMAL(10,2) DEFAULT 0 COMMENT '4月用量',
        Apr_ShuiFei DECIMAL(10,2) DEFAULT 0 COMMENT '4月水费',

        May_ZhiMa DECIMAL(10,2) DEFAULT 0 COMMENT '5月止码',
        May_YongLiang DECIMAL(10,2) DEFAULT 0 COMMENT '5月用量',
        May_ShuiFei DECIMAL(10,2) DEFAULT 0 COMMENT '5月水费',

        BeiLv DECIMAL(10,4) DEFAULT 1.0000 COMMENT '倍率',
        FeiLv DECIMAL(10,4) DEFAULT 0.0000 COMMENT '费率',
        BeiZhu TEXT COMMENT '备注信息',

        ChuangJianShiJian TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        GengXinShiJian TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
      ) COMMENT='月度抄表记录表';
    `);


    // 插入测试用户数据
    await connection.query(`
      INSERT INTO YongHu (ShouJiHao, XingMing, BuMen, JueSe, ShouQuan, QuanXianJiBie) VALUES
      ('13800138000', '系统管理员', '水务管理部', '系统管理员', true, 1),
      ('13900139000', '抄表员A', '设备维护部', '抄表员', true, 2),
      ('13700137000', '数据查看员', '运营管理部', '数据分析员', true, 3),
      ('18888888888', '超级管理员', '系统管理部', '超级管理员', true, 1)
      ON DUPLICATE KEY UPDATE
        XingMing=VALUES(XingMing),
        BuMen=VALUES(BuMen),
        JueSe=VALUES(JueSe),
        ShouQuan=VALUES(ShouQuan),
        QuanXianJiBie=VALUES(QuanXianJiBie);
    `);


    // 插入水表类型数据
    await connection.query(`
      INSERT INTO BiaoDianLeiXing (LeiXingMingCheng, RiChaoBiao, YueChaoBiaoLeiXing, ShiFouQiYong) VALUES
      ('生活水泵房', true, NULL, true),
      ('中水机房', true, NULL, true),
      ('室外消防总表', true, NULL, true),
      ('室外生活总表', true, NULL, true),
      ('隔油间', false, '隔油间', true),
      ('空调机房', false, '空调机房', true),
      ('卫生间', false, '卫生间', true),
      ('其它用水', false, '其它用水', true),
      ('商铺', false, '商铺', true),
      ('T1', false, 'T1', true),
      ('T2', false, 'T2', true)
      ON DUPLICATE KEY UPDATE LeiXingMingCheng=VALUES(LeiXingMingCheng), RiChaoBiao=VALUES(RiChaoBiao), YueChaoBiaoLeiXing=VALUES(YueChaoBiaoLeiXing), ShiFouQiYong=VALUES(ShiFouQiYong);
    `);
    // 数据库初始化完成
  } catch (error) {
    // 数据库初始化错误
  } finally {
    await connection.end();
  }
}

// 导出初始化函数，以便在应用启动时调用
module.exports = initializeDatabase; 