﻿package com.example.watermeterapp.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.watermeterapp.databinding.ItemMeterBinding
import com.example.watermeterapp.data.model.MeterInfo

/**
 * 水表列表适配器
 */
class MeterAdapter(private val onItemClick: (String) -> Unit) :
    RecyclerView.Adapter<MeterAdapter.MeterViewHolder>() {

    private val meterInfos = mutableListOf<MeterInfo>()

    /**
     * 更新数据
     */
    fun updateData(newMeterInfos: List<MeterInfo>) {
        meterInfos.clear()
        meterInfos.addAll(newMeterInfos)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MeterViewHolder {
        val binding = ItemMeterBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MeterViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MeterViewHolder, position: Int) {
        val meterInfo = meterInfos[position]
        holder.bind(meterInfo)
    }

    override fun getItemCount(): Int = meterInfos.size

    inner class MeterViewHolder(private val binding: ItemMeterBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(meterInfo: MeterInfo) {
            binding.tvMeterName.text = meterInfo.meterType
            
            // 显示上次读数
            if (meterInfo.lastReading != null) {
                binding.tvLastReading.text = "上次读数: ${meterInfo.lastReading}"
            } else {
                binding.tvLastReading.text = "上次读数: --"
            }
            
            // 显示上次抄表时间
            if (meterInfo.lastReadingDate != null) {
                binding.tvLastDate.text = "上次抄表时间: ${meterInfo.lastReadingDate}"
            } else {
                binding.tvLastDate.text = "上次抄表时间: --"
            }

            // 点击填写读数按钮
            binding.btnFillReading.setOnClickListener {
                onItemClick(meterInfo.meterType)
            }
        }
    }
}
