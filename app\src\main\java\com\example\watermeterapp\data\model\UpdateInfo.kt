package com.example.watermeterapp.data.model

import java.io.Serializable

/**
 * 应用更新信息数据模型
 */
data class UpdateInfo(
    val versionCode: Int,           // 版本号
    val versionName: String,        // 版本名称
    val updateTitle: String,        // 更新标题
    val updateContent: String,      // 更新内容描述
    val downloadUrl: String,        // APK下载地址
    val fileSize: Long,            // 文件大小（字节）
    val fileMd5: String,           // 文件MD5校验值
    val isForceUpdate: Boolean,     // 是否强制更新
    val minSupportVersion: Int,     // 最低支持版本
    val releaseTime: String,        // 发布时间
    val updateFeatures: List<String> = emptyList(), // 更新特性列表
    val packageName: String = "",   // 新版本的应用包名
    val oldPackageName: String = "", // 旧版本的应用包名
    val installMode: String = ""    // 安装模式，如"AUTO_INSTALL"表示使用应用内更新
) : Serializable

/**
 * 版本检查响应
 */
data class VersionCheckResponse(
    val code: Int,
    val message: String,
    val data: UpdateInfo?
) : Serializable
