﻿package com.example.watermeterapp.utils

import android.content.Context
import android.content.SharedPreferences

/**
 * SharedPreferences工具类
 * 用于保存和获取应用的配置信息
 */
object PrefsUtil {

    private const val PREFS_NAME = "water_meter_app_prefs"
    private const val KEY_PHONE_NUMBER = "phone_number"
    private const val KEY_JWT_TOKEN = "jwt_token"
    private const val KEY_USER_NAME = "user_name"
    private const val KEY_USER_DEPARTMENT = "user_department"
    private const val KEY_USER_ROLE = "user_role"
    private const val KEY_USER_AUTHORIZED = "user_authorized"
    private const val KEY_USER_AUTH_LEVEL = "user_auth_level"

    /**
     * 获取SharedPreferences实例
     */
    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    /**
     * 保存手机号
     */
    fun savePhoneNumber(context: Context, phoneNumber: String) {
        getPrefs(context).edit().apply {
            putString(KEY_PHONE_NUMBER, phoneNumber)
            apply()
        }
    }

    /**
     * 获取保存的手机号
     */
    fun getPhoneNumber(context: Context): String {
        return getPrefs(context).getString(KEY_PHONE_NUMBER, "") ?: ""
    }

    /**
     * 保存JWT Token
     */
    fun saveJwtToken(context: Context, token: String) {
        getPrefs(context).edit().apply {
            putString(KEY_JWT_TOKEN, token)
            apply()
        }
    }

    /**
     * 获取保存的JWT Token
     * @return 已保存的JWT Token，如果没有则返回null
     */
    fun getJwtToken(context: Context): String? {
        return getPrefs(context).getString(KEY_JWT_TOKEN, null)
    }

    /**
     * 保存用户名
     */
    fun saveUserName(context: Context, name: String) {
        getPrefs(context).edit().apply {
            putString(KEY_USER_NAME, name)
            apply()
        }
    }

    /**
     * 获取保存的用户名
     */
    fun getUserName(context: Context): String {
        return getPrefs(context).getString(KEY_USER_NAME, "") ?: ""
    }

    /**
     * 保存用户部门
     */
    fun saveUserDepartment(context: Context, department: String) {
        getPrefs(context).edit().apply {
            putString(KEY_USER_DEPARTMENT, department)
            apply()
        }
    }

    /**
     * 获取保存的用户部门
     */
    fun getUserDepartment(context: Context): String {
        return getPrefs(context).getString(KEY_USER_DEPARTMENT, "") ?: ""
    }

    /**
     * 保存用户角色
     */
    fun saveUserRole(context: Context, role: String) {
        getPrefs(context).edit().apply {
            putString(KEY_USER_ROLE, role)
            apply()
        }
    }

    /**
     * 获取保存的用户角色
     */
    fun getUserRole(context: Context): String {
        return getPrefs(context).getString(KEY_USER_ROLE, "") ?: ""
    }

    /**
     * 保存用户授权状态
     */
    fun saveUserAuthorized(context: Context, authorized: Boolean) {
        getPrefs(context).edit().apply {
            putBoolean(KEY_USER_AUTHORIZED, authorized)
            apply()
        }
    }

    /**
     * 获取保存的用户授权状态
     */
    fun isUserAuthorized(context: Context): Boolean {
        return getPrefs(context).getBoolean(KEY_USER_AUTHORIZED, false)
    }

    /**
     * 检查是否有有效的登录信息
     */
    fun hasValidLogin(context: Context): Boolean {
        val phone = getPhoneNumber(context)
        val authorized = isUserAuthorized(context)
        val token = getJwtToken(context)

        val isValid = phone.isNotEmpty() && authorized && !token.isNullOrEmpty()

        // 所有用户都需要：手机号、授权状态和有效的JWT token
        return isValid
    }

    /**
     * 清除所有用户信息
     */
    fun clearAllUserInfo(context: Context) {
        getPrefs(context).edit().apply {
            remove(KEY_PHONE_NUMBER)
            remove(KEY_JWT_TOKEN)
            remove(KEY_USER_NAME)
            remove(KEY_USER_DEPARTMENT)
            remove(KEY_USER_ROLE)
            remove(KEY_USER_AUTHORIZED)
            remove(KEY_USER_AUTH_LEVEL)
            apply()
        }
    }
}
