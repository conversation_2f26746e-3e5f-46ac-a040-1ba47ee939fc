package com.example.watermeterapp

import android.content.Context
import android.util.Log
import com.example.watermeterapp.data.api.ApiClient
import com.example.watermeterapp.data.model.ApiResponse
import com.example.watermeterapp.data.model.MonthlyMeterType
import com.example.watermeterapp.data.model.User
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * 月度抄表API示例类
 * 此类展示如何使用现有代码请求服务器月度抄表数据
 * 注意：此类仅作为示例，不修改现有代码
 */
object MonthlyMeterApiExample {
    private const val TAG = "MonthlyMeterApiExample"
    private const val SERVER_URL = "http://223.76.187.251:3002/api/"
    private const val USER_PHONE = "13995944332"

    /**
     * 初始化API客户端
     */
    fun initialize(context: Context) {
        // 设置API客户端上下文
        ApiClient.setContext(context)
        
        // 设置服务器地址
        ApiClient.setCustomServerUrl(SERVER_URL)
        
        Log.d(TAG, "API客户端初始化完成，服务器地址: $SERVER_URL")
    }
    
    /**
     * 登录并获取月度抄表数据
     */
    fun loginAndGetMonthlyMeterData(
        context: Context,
        onLoginSuccess: (User) -> Unit,
        onMonthlyDataReceived: (List<MonthlyMeterType>) -> Unit,
        onError: (String) -> Unit
    ) {
        // 1. 登录获取令牌
        login(context, onLoginSuccess = { user ->
            // 登录成功，调用回调
            onLoginSuccess(user)
            
            // 2. 获取月度抄表数据
            getMonthlyMeterTypes(onSuccess = onMonthlyDataReceived, onError = onError)
            
        }, onError = onError)
    }
    
    /**
     * 登录
     */
    private fun login(
        context: Context,
        onLoginSuccess: (User) -> Unit,
        onError: (String) -> Unit
    ) {
        Log.d(TAG, "开始登录，用户手机号: $USER_PHONE")
        
        // 调用登录API
        ApiClient.apiService.login(USER_PHONE).enqueue(object : Callback<ApiResponse<User>> {
            override fun onResponse(call: Call<ApiResponse<User>>, response: Response<ApiResponse<User>>) {
                if (response.isSuccessful) {
                    val apiResponse = response.body()
                    
                    if (apiResponse?.code == 0 && apiResponse.data != null) {
                        val user = apiResponse.data
                        
                        // 保存JWT Token
                        user.token?.let { token ->
                            Log.d(TAG, "登录成功，获取到令牌")
                            // 保存令牌到SharedPreferences
                            com.example.watermeterapp.utils.PrefsUtil.saveJwtToken(context, token)
                            
                            // 调用登录成功回调
                            onLoginSuccess(user)
                        } ?: run {
                            onError("登录响应中没有令牌")
                        }
                    } else {
                        onError(apiResponse?.message ?: "登录失败")
                    }
                } else {
                    onError("登录失败: ${response.code()} - ${response.message()}")
                }
            }
            
            override fun onFailure(call: Call<ApiResponse<User>>, t: Throwable) {
                onError("网络错误: ${t.message}")
            }
        })
    }
    
    /**
     * 获取月度抄表类型
     */
    private fun getMonthlyMeterTypes(
        onSuccess: (List<MonthlyMeterType>) -> Unit,
        onError: (String) -> Unit
    ) {
        Log.d(TAG, "开始获取月度抄表类型")
        
        // 调用获取月度抄表类型API
        ApiClient.apiService.getMonthlyMeterTypesWithSubTypes().enqueue(object : Callback<ResponseBody> {
            override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                if (response.isSuccessful) {
                    try {
                        val body = response.body()?.string() ?: ""
                        Log.d(TAG, "获取到月度抄表数据，开始解析")
                        
                        val meterTypes = parseMeterTypes(body)
                        
                        if (meterTypes.isNotEmpty()) {
                            Log.d(TAG, "解析成功，获取到${meterTypes.size}个月度抄表类型")
                            
                            // 打印月度抄表类型信息
                            meterTypes.forEachIndexed { index, type ->
                                Log.d(TAG, "${index + 1}. ${type.parentType} (${type.subTypes.size}个子类型)")
                                type.subTypes.forEachIndexed { subIndex, subType ->
                                    val noMeterMark = if (subType.hasNoMeter) "[无表]" else ""
                                    Log.d(TAG, "   ${subIndex + 1}. ${subType.name} $noMeterMark")
                                }
                            }
                            
                            // 调用成功回调
                            onSuccess(meterTypes)
                        } else {
                            onError("没有获取到月度抄表类型")
                        }
                    } catch (e: Exception) {
                        onError("解析数据失败: ${e.message}")
                    }
                } else {
                    onError("获取月度抄表类型失败: ${response.code()} - ${response.message()}")
                }
            }
            
            override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                onError("网络错误: ${t.message}")
            }
        })
    }
    
    /**
     * 解析月抄表类型数据
     * 注意：此方法复制自MonthlyReadingActivity，以保持一致性
     */
    private fun parseMeterTypes(body: String): List<MonthlyMeterType> {
        val gson = Gson()

        try {
            // 尝试解析为标准API响应格式
            val apiResponse = gson.fromJson(body, ApiResponse::class.java)

            if (apiResponse.code == 0 && apiResponse.data != null) {
                // 解析data字段为月抄表类型列表
                val dataJson = gson.toJson(apiResponse.data)

                val type = object : TypeToken<List<MonthlyMeterType>>() {}.type
                val result = gson.fromJson<List<MonthlyMeterType>>(dataJson, type)
                return result
            }
        } catch (e: Exception) {
            // 备用解析：尝试直接解析为月抄表类型数组
            val trimmedBody = body.trim()

            if (trimmedBody.startsWith("[") && trimmedBody.endsWith("]")) {
                // JSON数组格式
                val type = object : TypeToken<List<MonthlyMeterType>>() {}.type
                try {
                    val result = gson.fromJson<List<MonthlyMeterType>>(trimmedBody, type)
                    return result
                } catch (e2: Exception) {
                    // 尝试解析为简单字符串列表，然后转换为MonthlyMeterType对象
                    val stringType = object : TypeToken<List<String>>() {}.type
                    val stringList = gson.fromJson<List<String>>(trimmedBody, stringType)
                    return stringList.map { MonthlyMeterType(it, emptyList(), false) }
                }
            } else {
                // 简单字符串格式，按分隔符拆分
                val result = trimmedBody.split(",", ";", "\n")
                    .map { it.trim().replace("\"", "") }
                    .filter { it.isNotEmpty() }
                    .map { MonthlyMeterType(it, emptyList(), false) }
                return result
            }
        }

        return emptyList()
    }
    
    /**
     * 打印月度抄表数据（示例用）
     */
    fun printMonthlyMeterData(meterTypes: List<MonthlyMeterType>): String {
        val sb = StringBuilder()
        sb.appendLine("===== 月度抄表数据 =====")
        sb.appendLine("获取到${meterTypes.size}个月度抄表类型:")
        
        meterTypes.forEachIndexed { index, type ->
            sb.appendLine("${index + 1}. ${type.parentType} (${type.subTypes.size}个子类型)")
            type.subTypes.forEachIndexed { subIndex, subType ->
                val noMeterMark = if (subType.hasNoMeter) "[无表]" else ""
                sb.appendLine("   ${subIndex + 1}. ${subType.name} $noMeterMark")
            }
        }
        
        return sb.toString()
    }
    
    /**
     * 获取示例月度抄表数据（用于展示）
     */
    fun getExampleMonthlyMeterData(): List<MonthlyMeterType> {
        // 返回模拟的月度抄表数据，与服务器返回格式一致
        return listOf(
            MonthlyMeterType("隔油间", listOf(
                MonthlyMeterType.SubMeterType("1#隔油间"),
                MonthlyMeterType.SubMeterType("2#隔油间"),
                MonthlyMeterType.SubMeterType("3#隔油间", hasNoMeter = true)
            )),
            MonthlyMeterType("生活水泵房", listOf(
                MonthlyMeterType.SubMeterType("1#水泵"),
                MonthlyMeterType.SubMeterType("2#水泵")
            )),
            MonthlyMeterType("中水机房", listOf(
                MonthlyMeterType.SubMeterType("进水总表"),
                MonthlyMeterType.SubMeterType("出水总表")
            ))
        )
    }
    
    /**
     * 获取示例月度抄表数据的JSON格式
     */
    fun getExampleMonthlyMeterDataJson(): String {
        val apiResponse = ApiResponse(
            code = 0,
            message = "获取成功",
            data = getExampleMonthlyMeterData()
        )
        return Gson().toJson(apiResponse)
    }
} 