package com.example.watermeterapp.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import pub.devrel.easypermissions.EasyPermissions

/**
 * 权限工具类
 */
object PermissionUtils {
    
    // 权限请求码
    const val CAMERA_PERMISSION_REQUEST_CODE = 100
    const val STORAGE_PERMISSION_REQUEST_CODE = 101
    
    // 相机权限
    private val CAMERA_PERMISSIONS = arrayOf(
        Manifest.permission.CAMERA
    )
    
    // 存储权限
    private val STORAGE_PERMISSIONS = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE
        )
    } else {
        arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
    }
    
    /**
     * 检查是否有相机权限
     */
    fun hasCameraPermission(context: Context): <PERSON><PERSON>an {
        return EasyPermissions.hasPermissions(context, *CAMERA_PERMISSIONS)
    }
    
    /**
     * 请求相机权限
     */
    fun requestCameraPermission(activity: Activity, rationale: String) {
        EasyPermissions.requestPermissions(
            activity,
            rationale,
            CAMERA_PERMISSION_REQUEST_CODE,
            *CAMERA_PERMISSIONS
        )
    }
    
    /**
     * 检查是否有存储权限
     */
    fun hasStoragePermission(context: Context): Boolean {
        return EasyPermissions.hasPermissions(context, *STORAGE_PERMISSIONS)
    }
    
    /**
     * 请求存储权限
     */
    fun requestStoragePermission(activity: Activity, rationale: String) {
        EasyPermissions.requestPermissions(
            activity,
            rationale,
            STORAGE_PERMISSION_REQUEST_CODE,
            *STORAGE_PERMISSIONS
        )
    }
    
    /**
     * 请求所有必要权限
     */
    fun requestAllPermissions(activity: Activity) {
        val allPermissions = CAMERA_PERMISSIONS + STORAGE_PERMISSIONS
        EasyPermissions.requestPermissions(
            activity,
            "应用需要相机和存储权限才能正常工作",
            CAMERA_PERMISSION_REQUEST_CODE,
            *allPermissions
        )
    }
    
    /**
     * 检查是否有所有必要权限
     */
    fun hasAllPermissions(context: Context): Boolean {
        val allPermissions = CAMERA_PERMISSIONS + STORAGE_PERMISSIONS
        return EasyPermissions.hasPermissions(context, *allPermissions)
    }
}
