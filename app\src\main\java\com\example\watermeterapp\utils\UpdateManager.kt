package com.example.watermeterapp.utils

import android.app.Activity
import android.app.AlertDialog
import android.app.DownloadManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import android.widget.Toast
import androidx.core.content.FileProvider
import com.example.watermeterapp.BuildConfig
import com.example.watermeterapp.R
import com.example.watermeterapp.data.api.ApiClient
import com.example.watermeterapp.data.model.UpdateInfo
import com.example.watermeterapp.data.model.VersionCheckResponse
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.io.File
import java.security.MessageDigest

/**
 * 应用更新管理器
 */
object UpdateManager {
    
    private var downloadId: Long = -1
    private var updateInfo: UpdateInfo? = null
    private var downloadReceiver: BroadcastReceiver? = null
    
    // 保存旧版本包名的键
    private const val PREF_OLD_PACKAGE_NAME = "old_package_name"
    
    /**
     * 检查应用更新
     */
    fun checkUpdate(context: Context, showNoUpdateDialog: Boolean = false) {
        val currentVersionCode = try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                context.packageManager.getPackageInfo(
                    context.packageName,
                    PackageManager.PackageInfoFlags.of(0)
                ).longVersionCode.toInt()
            } else {
                @Suppress("DEPRECATION")
                context.packageManager.getPackageInfo(context.packageName, 0).versionCode
            }
        } catch (e: Exception) {
            BuildConfig.VERSION_CODE
        }
        
        ApiClient.apiService.checkVersion(currentVersionCode).enqueue(object : Callback<VersionCheckResponse> {
            override fun onResponse(call: Call<VersionCheckResponse>, response: Response<VersionCheckResponse>) {
                if (response.isSuccessful) {
                    val versionResponse = response.body()
                    if (versionResponse?.code == 0 && versionResponse.data != null) {
                        val updateInfo = versionResponse.data
                        if (updateInfo.versionCode > currentVersionCode) {
                            // 有新版本可用
                            showUpdateDialog(context, updateInfo)
                        } else if (showNoUpdateDialog) {
                            // 已是最新版本
                            showNoUpdateDialog(context)
                        }
                    } else if (showNoUpdateDialog) {
                        showNoUpdateDialog(context)
                    }
                } else {
                    if (showNoUpdateDialog) {
                        Toast.makeText(context, "检查更新失败", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            
            override fun onFailure(call: Call<VersionCheckResponse>, t: Throwable) {
                if (showNoUpdateDialog) {
                    Toast.makeText(context, "检查更新失败: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            }
        })
    }
    
    /**
     * 显示更新对话框
     */
    private fun showUpdateDialog(context: Context, updateInfo: UpdateInfo) {
        this.updateInfo = updateInfo
        
        val dialogBuilder = AlertDialog.Builder(context)
            .setTitle("发现新版本 ${updateInfo.versionName}")
            .setMessage(buildUpdateMessage(updateInfo))
            .setPositiveButton("立即更新") { _, _ ->
                startDownload(context, updateInfo)
            }
        
        if (!updateInfo.isForceUpdate) {
            dialogBuilder.setNegativeButton("稍后更新") { dialog, _ ->
                dialog.dismiss()
            }
        }
        
        val dialog = dialogBuilder.create()
        dialog.setCancelable(!updateInfo.isForceUpdate)
        dialog.show()
    }
    
    /**
     * 构建更新消息
     */
    private fun buildUpdateMessage(updateInfo: UpdateInfo): String {
        val sb = StringBuilder()
        sb.append("版本：${updateInfo.versionName}\n")
        sb.append("大小：${formatFileSize(updateInfo.fileSize)}\n")
        sb.append("发布时间：${updateInfo.releaseTime}\n\n")
        
        if (updateInfo.updateContent.isNotEmpty()) {
            sb.append("更新内容：\n${updateInfo.updateContent}\n\n")
        }
        
        if (updateInfo.updateFeatures.isNotEmpty()) {
            sb.append("新增功能：\n")
            updateInfo.updateFeatures.forEach { feature ->
                sb.append("• $feature\n")
            }
        }
        
        if (updateInfo.isForceUpdate) {
            sb.append("\n⚠️ 此版本为强制更新，请立即更新以继续使用应用")
        }
        
        return sb.toString()
    }
    
    /**
     * 显示无更新对话框
     */
    private fun showNoUpdateDialog(context: Context) {
        AlertDialog.Builder(context)
            .setTitle("检查更新")
            .setMessage("当前已是最新版本")
            .setPositiveButton("确定") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
    
    /**
     * 开始下载APK
     */
    private fun startDownload(context: Context, updateInfo: UpdateInfo) {
        // 检查存储权限
        if (!PermissionUtils.hasStoragePermission(context)) {
            if (context is Activity) {
                PermissionUtils.requestStoragePermission(context, "下载更新需要存储权限")
            }
            return
        }
        
        // 检查是否允许安装未知来源应用
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (!context.packageManager.canRequestPackageInstalls()) {
                requestInstallPermission(context)
                return
            }
        }
        
        val fileName = "watermeter_${updateInfo.versionName}.apk"
        val request = DownloadManager.Request(Uri.parse(updateInfo.downloadUrl))
            .setTitle("水表抄录系统")
            .setDescription("正在下载新版本...")
            .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
            .setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName)
            .setAllowedOverMetered(true)
            .setAllowedOverRoaming(true)
        
        val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        downloadId = downloadManager.enqueue(request)
        
        // 注册下载完成监听器
        registerDownloadReceiver(context)
        
        Toast.makeText(context, "开始下载更新...", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 请求安装权限
     */
    private fun requestInstallPermission(context: Context) {
        AlertDialog.Builder(context)
            .setTitle("需要安装权限")
            .setMessage("为了安装更新，需要允许安装未知来源应用")
            .setPositiveButton("去设置") { _, _ ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES)
                    intent.data = Uri.parse("package:${context.packageName}")
                    context.startActivity(intent)
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 注册下载完成监听器
     */
    private fun registerDownloadReceiver(context: Context) {
        downloadReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val id = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
                if (id == downloadId) {
                    handleDownloadComplete(context)
                }
            }
        }
        
        val filter = IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE)
        context.registerReceiver(downloadReceiver, filter)
    }
    
    /**
     * 处理下载完成
     */
    private fun handleDownloadComplete(context: Context) {
        val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        val query = DownloadManager.Query().setFilterById(downloadId)
        val cursor = downloadManager.query(query)
        
        if (cursor.moveToFirst()) {
            val status = cursor.getInt(cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_STATUS))
            if (status == DownloadManager.STATUS_SUCCESSFUL) {
                val localUri = cursor.getString(cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_LOCAL_URI))
                val file = File(Uri.parse(localUri).path!!)
                
                // 验证文件MD5
                if (updateInfo != null && verifyFileMd5(file, updateInfo!!.fileMd5)) {
                    // 保存旧版本包名，用于安装后卸载
                    if (updateInfo?.oldPackageName?.isNotEmpty() == true) {
                        saveOldPackageName(context, updateInfo?.oldPackageName ?: "")
                    }
                    
                    installApk(context, file)
                } else {
                    Toast.makeText(context, "下载文件校验失败，请重新下载", Toast.LENGTH_LONG).show()
                }
            } else {
                Toast.makeText(context, "下载失败，请重试", Toast.LENGTH_SHORT).show()
            }
        }
        
        cursor.close()
        unregisterDownloadReceiver(context)
    }
    
    /**
     * 验证文件MD5
     */
    private fun verifyFileMd5(file: File, expectedMd5: String): Boolean {
        if (expectedMd5.isEmpty()) return true // 如果没有提供MD5，跳过验证
        
        try {
            val md = MessageDigest.getInstance("MD5")
            val inputStream = file.inputStream()
            val buffer = ByteArray(8192)
            var bytesRead: Int
            
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                md.update(buffer, 0, bytesRead)
            }
            
            inputStream.close()
            
            val digest = md.digest()
            val sb = StringBuilder()
            for (byte in digest) {
                sb.append(String.format("%02x", byte))
            }
            
            return sb.toString().equals(expectedMd5, ignoreCase = true)
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
    }
    
    /**
     * 安装APK
     */
    private fun installApk(context: Context, file: File) {
        val intent = Intent(Intent.ACTION_VIEW)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )
            intent.setDataAndType(uri, "application/vnd.android.package-archive")
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            
            // 添加安装包相关参数
            intent.putExtra(Intent.EXTRA_NOT_UNKNOWN_SOURCE, true)
            intent.putExtra(Intent.EXTRA_INSTALLER_PACKAGE_NAME, context.packageName)
        } else {
            intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive")
        }
        
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }
    
    /**
     * 注销下载监听器
     */
    private fun unregisterDownloadReceiver(context: Context) {
        downloadReceiver?.let {
            try {
                context.unregisterReceiver(it)
            } catch (e: Exception) {
                // 忽略异常
            }
            downloadReceiver = null
        }
    }
    
    /**
     * 格式化文件大小
     */
    private fun formatFileSize(size: Long): String {
        val kb = 1024.0
        val mb = kb * 1024
        val gb = mb * 1024
        
        return when {
            size >= gb -> String.format("%.1f GB", size / gb)
            size >= mb -> String.format("%.1f MB", size / mb)
            size >= kb -> String.format("%.1f KB", size / kb)
            else -> "$size B"
        }
    }
    
    /**
     * 保存旧版本包名
     */
    private fun saveOldPackageName(context: Context, oldPackageName: String) {
        val prefs = context.getSharedPreferences("update_manager_prefs", Context.MODE_PRIVATE)
        prefs.edit().putString(PREF_OLD_PACKAGE_NAME, oldPackageName).apply()
    }
    
    /**
     * 获取保存的旧版本包名
     */
    private fun getOldPackageName(context: Context): String? {
        val prefs = context.getSharedPreferences("update_manager_prefs", Context.MODE_PRIVATE)
        return prefs.getString(PREF_OLD_PACKAGE_NAME, null)
    }
    
    /**
     * 检查并卸载旧版本
     * 在应用启动时调用此方法
     */
    fun checkAndUninstallOldVersion(context: Context) {
        val oldPackageName = getOldPackageName(context)
        
        if (!oldPackageName.isNullOrEmpty() && isPackageInstalled(context, oldPackageName)) {
            showUninstallOldVersionDialog(context, oldPackageName)
        }
    }
    
    /**
     * 检查包是否已安装
     */
    private fun isPackageInstalled(context: Context, packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    /**
     * 显示卸载旧版本对话框
     */
    private fun showUninstallOldVersionDialog(context: Context, packageName: String) {
        AlertDialog.Builder(context)
            .setTitle("卸载旧版本")
            .setMessage("检测到旧版本应用，是否卸载？")
            .setPositiveButton("卸载") { _, _ ->
                uninstallPackage(context, packageName)
            }
            .setNegativeButton("稍后") { _, _ ->
                // 不执行卸载，但保留记录
            }
            .show()
    }
    
    /**
     * 卸载指定包名的应用
     */
    private fun uninstallPackage(context: Context, packageName: String) {
        val intent = Intent(Intent.ACTION_DELETE)
        intent.data = Uri.parse("package:$packageName")
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
        
        // 卸载后清除记录
        val prefs = context.getSharedPreferences("update_manager_prefs", Context.MODE_PRIVATE)
        prefs.edit().remove(PREF_OLD_PACKAGE_NAME).apply()
    }
}
