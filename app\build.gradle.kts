plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace = "com.example.watermeterapp"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.example.watermeterapp"
        minSdk = 24
        targetSdk = 35
        versionCode = 2
        versionName = "2.2"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
        
        multiDexEnabled = true
    }

    signingConfigs {
        create("release") {
            // 生产环境签名配置 - 使用默认调试签名
            storeFile = file("${System.getProperty("user.home")}/.android/debug.keystore")
            storePassword = "android"
            keyAlias = "androiddebugkey"
            keyPassword = "android"
        }
    }

    buildTypes {
        debug {
            isDebuggable = true
            applicationIdSuffix = ".debug"
            // 开发环境服务器配置 - 默认使用本地服务器
            buildConfigField("String", "SERVER_HOST", "\"***************\"")
            buildConfigField("int", "SERVER_PORT", "3002")
            buildConfigField("String", "BASE_URL", "\"http://***************:3002/api/\"")
            buildConfigField("boolean", "SHOW_SERVER_CONFIG", "true")
        }
        release {
            isMinifyEnabled = true  // 启用代码混淆
            isShrinkResources = true  // 启用资源压缩
            signingConfig = signingConfigs.getByName("release")
            proguardFiles(
                getDefaultProguardFile("proguard-android.txt"),  // 使用更保守的配置
                "proguard-rules.pro"
            )
            // 生产环境服务器配置
            buildConfigField("String", "SERVER_HOST", "\"**************\"")
            buildConfigField("int", "SERVER_PORT", "3002")
            buildConfigField("String", "BASE_URL", "\"http://**************:3002/api/\"")
            buildConfigField("boolean", "SHOW_SERVER_CONFIG", "false")
        }
        create("releaseNoObfuscation") {
            initWith(getByName("release"))
            isMinifyEnabled = false  // 禁用代码混淆
            isShrinkResources = false  // 禁用资源压缩
            applicationIdSuffix = ".noobf"
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
}

dependencies {
    // Android核心库
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)

    // UI组件
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("com.google.android.material:material:1.11.0")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("androidx.recyclerview:recyclerview:1.3.2")
    implementation("androidx.cardview:cardview:1.0.0")

    // 网络请求库
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")

    // 图片处理
    implementation("com.github.bumptech.glide:glide:4.16.0")
    annotationProcessor("com.github.bumptech.glide:compiler:4.16.0")

    // 权限处理
    implementation("pub.devrel:easypermissions:3.0.0")

    // 生命周期组件
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.7.0")

    // Activity和Fragment
    implementation("androidx.activity:activity-ktx:1.8.2")
    implementation("androidx.fragment:fragment-ktx:1.6.2")
}