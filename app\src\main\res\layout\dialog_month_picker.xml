<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="选择抄表月份"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tvMonthHint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="当前智能推荐月份"
        android:textSize="14sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:layout_marginBottom="8dp" />

    <NumberPicker
        android:id="@+id/npMonth"
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:layout_marginVertical="16dp"
        android:theme="@style/NumberPickerStyle" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="取消"
            android:textColor="@color/text_secondary"
            android:background="@drawable/button_outline"
            style="?android:attr/borderlessButtonStyle" />

        <Button
            android:id="@+id/btnConfirm"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="确认"
            android:textColor="@color/white"
            android:background="@drawable/button_primary" />

    </LinearLayout>

</LinearLayout>
