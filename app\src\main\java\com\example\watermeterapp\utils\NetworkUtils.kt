package com.example.watermeterapp.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.widget.Toast
import com.example.watermeterapp.data.api.ApiClient
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * 网络工具类
 * 用于检查网络连接状态和服务器连接
 */
object NetworkUtils {
    private const val TAG = "NetworkUtils"

    /**
     * 检查网络是否连接
     */
    fun isNetworkConnected(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            
            return capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                   capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                   capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            return networkInfo != null && networkInfo.isConnected
        }
    }

    /**
     * 检查服务器连接
     * 显示Toast消息提示结果
     */
    fun checkServerConnection(context: Context, onResult: (Boolean) -> Unit) {
        if (!isNetworkConnected(context)) {
            Toast.makeText(context, "无网络连接", Toast.LENGTH_SHORT).show()
            onResult(false)
            return
        }
        
        Toast.makeText(context, "正在检查服务器连接...", Toast.LENGTH_SHORT).show()
        
        ApiClient.apiService.getDailyMeterTypes().enqueue(object : Callback<ResponseBody> {
            override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                val isConnected = response.isSuccessful
                val message = if (isConnected) "服务器连接正常" else "服务器连接失败: ${response.code()}"
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                onResult(isConnected)
            }
            
            override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                Toast.makeText(context, "服务器连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
                onResult(false)
            }
        })
    }

    /**
     * 静默检查服务器连接
     * 不显示任何Toast消息
     */
    fun checkServerConnectionSilent(onResult: (Boolean) -> Unit) {
        ApiClient.apiService.getDailyMeterTypes().enqueue(object : Callback<ResponseBody> {
            override fun onResponse(call: Call<ResponseBody>, response: Response<ResponseBody>) {
                val isConnected = response.isSuccessful
                onResult(isConnected)
            }
            
            override fun onFailure(call: Call<ResponseBody>, t: Throwable) {
                onResult(false)
            }
        })
    }
}
