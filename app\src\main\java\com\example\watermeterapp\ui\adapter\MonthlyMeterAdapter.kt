package com.example.watermeterapp.ui.adapter

import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.example.watermeterapp.data.model.MonthlyMeterType
import com.example.watermeterapp.databinding.ItemMonthlyMeterBinding
import com.example.watermeterapp.databinding.ItemMonthlyMeterWithSearchBinding
import com.example.watermeterapp.databinding.ItemMonthlySubmeterBinding

/**
 * 月抄表类型及子模块适配器
 */
class MonthlyMeterAdapter(
    private val onSubTypeClick: (parentType: String, subTypeName: String) -> Unit,
    private val onScrollToPosition: (position: Int) -> Unit = {}
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    
    companion object {
        private const val TYPE_PARENT = 0
        private const val TYPE_PARENT_WITH_SEARCH = 1
        private const val TYPE_CHILD = 2
    }
    
    // 列表数据结构，用于展开/折叠
    private val items = mutableListOf<DisplayItem>()

    // 原始数据和过滤后的数据（用于搜索）
    private val originalData = mutableListOf<MonthlyMeterType>()
    private val searchFilters = mutableMapOf<String, String>() // 每个类型的搜索关键词
    
    /**
     * 更新数据
     */
    fun updateData(meterTypes: List<MonthlyMeterType>) {
        items.clear()

        // 为每个月抄表类型添加父项和子项
        meterTypes.forEach { meterType ->
            // 初始化所有子项的状态
            meterType.subTypes.forEach { subType ->
                if (subType.hasNoMeter) {
                    // 无表项自动设置为已提交状态
                    subType.isSubmitted = true
                    subType.isEnabled = false
                } else {
                    // 有表项初始化为未提交状态
                    subType.isSubmitted = false
                    subType.isEnabled = true
                }
            }

            // 添加父项
            items.add(DisplayItem.ParentItem(meterType))

            // 默认子项不展开
            meterType.expanded = false // 默认收缩
        }

        notifyDataSetChanged()
    }
    
    /**
     * 过滤子项（用于搜索）
     */
    private fun filterSubItems(meterType: MonthlyMeterType, position: Int) {
        if (!meterType.expanded) return

        val query = searchFilters[meterType.parentType] ?: ""

        // 移除当前的子项
        var nextPosition = position + 1
        var removeCount = 0
        while (nextPosition < items.size && items[nextPosition] is DisplayItem.ChildItem) {
            items.removeAt(nextPosition)
            removeCount++
        }
        if (removeCount > 0) {
            notifyItemRangeRemoved(position + 1, removeCount)
        }

        // 添加过滤后的子项
        val filteredSubTypes = if (query.isEmpty()) {
            meterType.subTypes.filter { !it.hasNoMeter }
        } else {
            meterType.subTypes.filter { !it.hasNoMeter && it.name.contains(query, ignoreCase = true) }
        }

        nextPosition = position + 1
        filteredSubTypes.forEach { subType ->
            items.add(nextPosition, DisplayItem.ChildItem(meterType.parentType, subType))
            nextPosition++
        }
        if (filteredSubTypes.isNotEmpty()) {
            notifyItemRangeInserted(position + 1, filteredSubTypes.size)
        }
    }

    /**
     * 更新父级徽章显示
     */
    private fun updateParentBadge(parentType: String) {
        // 找到对应的父项并更新徽章
        for (i in items.indices) {
            val item = items[i]
            if (item is DisplayItem.ParentItem && item.meterType.parentType == parentType) {
                notifyItemChanged(i)
                break
            }
        }
    }

    /**
     * 获取徽章显示的数量
     * 商铺：不显示徽章
     * 其他类型：显示剩余未提交数量
     */
    private fun getBadgeCount(meterType: MonthlyMeterType): Int {
        return if (meterType.parentType == "商铺") {
            0 // 商铺不显示徽章
        } else {
            // 其他类型显示剩余未提交数量
            meterType.subTypes.count { !it.hasNoMeter && (!it.isSubmitted || it.isEnabled) }
        }
    }

    /**
     * 标记子项为已提交
     */
    fun markAsSubmitted(parentType: String, subTypeName: String) {
        for (item in items) {
            if (item is DisplayItem.ChildItem &&
                item.parentType == parentType &&
                item.subType.name == subTypeName) {
                item.subType.isSubmitted = true
                item.subType.isEnabled = false
                notifyItemChanged(items.indexOf(item))
                updateParentBadge(parentType)
                break
            }
        }
    }

    /**
     * 切换父项的展开/折叠状态
     */
    private fun toggleExpand(position: Int) {
        if (position >= 0 && position < items.size) {
            val item = items[position]
            if (item is DisplayItem.ParentItem) {
                val meterType = item.meterType
                meterType.expanded = !meterType.expanded

                if (meterType.expanded) {
                    // 展开：添加过滤后的子项
                    filterSubItems(meterType, position)
                    // 延迟滚动，确保子项已经添加完成
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        onScrollToPosition(position)
                    }, 100)
                } else {
                    // 折叠：移除所有子项
                    var nextPosition = position + 1
                    var removeCount = 0
                    while (nextPosition < items.size && items[nextPosition] is DisplayItem.ChildItem) {
                        items.removeAt(nextPosition)
                        removeCount++
                    }
                    notifyItemRangeRemoved(position + 1, removeCount)
                }
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return when (val item = items[position]) {
            is DisplayItem.ParentItem -> {
                if (item.meterType.parentType == "商铺") TYPE_PARENT_WITH_SEARCH else TYPE_PARENT
            }
            is DisplayItem.ChildItem -> TYPE_CHILD
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_PARENT -> {
                val binding = ItemMonthlyMeterBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ParentViewHolder(binding)
            }
            TYPE_PARENT_WITH_SEARCH -> {
                val binding = ItemMonthlyMeterWithSearchBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ParentWithSearchViewHolder(binding)
            }
            TYPE_CHILD -> {
                val binding = ItemMonthlySubmeterBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                ChildViewHolder(binding)
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (val item = items[position]) {
            is DisplayItem.ParentItem -> {
                when (holder) {
                    is ParentViewHolder -> holder.bind(item.meterType, position)
                    is ParentWithSearchViewHolder -> holder.bind(item.meterType, position)
                }
            }
            is DisplayItem.ChildItem -> (holder as ChildViewHolder).bind(item.parentType, item.subType)
        }
    }

    override fun getItemCount(): Int = items.size

    inner class ParentViewHolder(private val binding: ItemMonthlyMeterBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(meterType: MonthlyMeterType, position: Int) {
            binding.tvMeterName.text = meterType.parentType

            // 设置展开/折叠图标
            binding.ivExpand.visibility = if (meterType.subTypes.isNotEmpty()) View.VISIBLE else View.GONE
            binding.ivExpand.rotation = if (meterType.expanded) 180f else 0f

            // 设置徽章显示
            val badgeCount = getBadgeCount(meterType)
            if (badgeCount > 0) {
                binding.tvBadge.visibility = View.VISIBLE
                binding.tvBadge.text = badgeCount.toString()
            } else {
                binding.tvBadge.visibility = View.GONE
            }

            // 点击展开/折叠
            binding.meterLayout.setOnClickListener {
                if (meterType.subTypes.isNotEmpty()) {
                    toggleExpand(position)
                    binding.ivExpand.rotation = if (meterType.expanded) 180f else 0f
                }
            }
        }
    }

    inner class ParentWithSearchViewHolder(private val binding: ItemMonthlyMeterWithSearchBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(meterType: MonthlyMeterType, position: Int) {
            binding.tvMeterName.text = meterType.parentType

            // 设置展开/折叠图标
            binding.ivExpand.visibility = if (meterType.subTypes.isNotEmpty()) View.VISIBLE else View.GONE
            binding.ivExpand.rotation = if (meterType.expanded) 180f else 0f

            // 设置徽章显示（商铺不显示徽章）
            val badgeCount = getBadgeCount(meterType)
            if (badgeCount > 0) {
                binding.tvBadge.visibility = View.VISIBLE
                binding.tvBadge.text = badgeCount.toString()
            } else {
                binding.tvBadge.visibility = View.GONE
            }

            // 显示搜索框（仅在展开时显示）
            binding.tilSearch.visibility = if (meterType.expanded) View.VISIBLE else View.GONE

            // 设置搜索监听
            binding.etSearch.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    val query = s?.toString() ?: ""
                    searchFilters[meterType.parentType] = query
                    filterSubItems(meterType, position)
                }
            })

            // 点击展开/折叠
            binding.meterLayout.setOnClickListener {
                if (meterType.subTypes.isNotEmpty()) {
                    toggleExpand(position)
                    binding.ivExpand.rotation = if (meterType.expanded) 180f else 0f
                    binding.tilSearch.visibility = if (meterType.expanded) View.VISIBLE else View.GONE
                }
            }
        }
    }

    inner class ChildViewHolder(private val binding: ItemMonthlySubmeterBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(parentType: String, subType: MonthlyMeterType.SubMeterType) {
            binding.tvSubMeterName.text = subType.name

            // 设置提交状态显示
            updateSubmissionState(subType)

            // 对于无表项，禁用所有交互
            if (subType.hasNoMeter) {
                binding.layoutSubMeter.setOnClickListener(null)
                binding.layoutSubMeter.setOnLongClickListener(null)
                binding.btnEnterReading.setOnClickListener(null)
                binding.btnEnterReading.text = "无表"
                binding.btnEnterReading.isEnabled = false
                binding.btnEnterReading.alpha = 0.5f // 按钮半透明表示禁用
            } else {
                // 整行点击处理 - 单击进入录入界面（仅限未提交或已重新激活的项目）
                binding.layoutSubMeter.setOnClickListener {
                    if (!subType.isSubmitted || subType.isReactivated) {
                        onSubTypeClick(parentType, subType.name)
                    } else {
                        // 已提交且未重新激活的项目，显示提示信息
                        Toast.makeText(binding.root.context, "该项目已提交，长按可重新激活", Toast.LENGTH_SHORT).show()
                    }
                }

                // 长按处理 - 切换启用/禁用状态（仅对已提交的项目有效）
                binding.layoutSubMeter.setOnLongClickListener {
                    if (subType.isSubmitted) {
                        subType.isEnabled = !subType.isEnabled
                        subType.isReactivated = subType.isEnabled // 设置重新激活状态
                        updateSubmissionState(subType)
                        updateParentBadge(parentType)

                        // 显示提示信息
                        val message = if (subType.isEnabled) "已重新激活" else "已禁用"
                        Toast.makeText(binding.root.context, message, Toast.LENGTH_SHORT).show()
                    }
                    true // 返回true表示消费了长按事件
                }

                // 按钮点击也进入录入界面（仅限未提交或已重新激活的项目）
                binding.btnEnterReading.setOnClickListener {
                    if (!subType.isSubmitted || subType.isReactivated) {
                        onSubTypeClick(parentType, subType.name)
                    } else {
                        // 已提交且未重新激活的项目，显示提示信息
                        Toast.makeText(binding.root.context, "该项目已提交，长按可重新激活", Toast.LENGTH_SHORT).show()
                    }
                }
                binding.btnEnterReading.text = binding.root.context.getString(com.example.watermeterapp.R.string.enter_reading)
                binding.btnEnterReading.isEnabled = true
                binding.btnEnterReading.alpha = 1.0f // 恢复正常透明度
            }
        }

        private fun updateSubmissionState(subType: MonthlyMeterType.SubMeterType) {
            if (subType.hasNoMeter) {
                // 无表项特殊样式 - 禁用状态，但要显示出来
                binding.layoutSubMeter.alpha = 0.6f // 稍微透明表示禁用
                binding.viewSubmittedIndicator.visibility = View.GONE // 不显示提交指示器

                // 设置文本显示，在名称后面加上红色的"（无表）"
                val spannableString = android.text.SpannableString("${subType.name}（无表）")
                val startIndex = subType.name.length
                val endIndex = spannableString.length

                // 设置红色
                spannableString.setSpan(
                    android.text.style.ForegroundColorSpan(binding.root.context.getColor(android.R.color.holo_red_dark)),
                    startIndex,
                    endIndex,
                    android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                binding.tvSubMeterName.text = spannableString
                binding.tvSubMeterName.setTextColor(binding.root.context.getColor(android.R.color.black)) // 主文本保持黑色
            } else if (subType.isSubmitted && !subType.isEnabled) {
                // 已提交且禁用状态
                binding.layoutSubMeter.alpha = 0.6f
                binding.viewSubmittedIndicator.visibility = View.VISIBLE
                binding.viewSubmittedIndicator.setBackgroundColor(binding.root.context.getColor(com.example.watermeterapp.R.color.success_color))
                binding.tvSubMeterName.setTextColor(binding.root.context.getColor(android.R.color.darker_gray))
                binding.tvSubMeterName.text = subType.name
            } else if (subType.isSubmitted && subType.isEnabled) {
                // 已提交但重新激活状态
                binding.layoutSubMeter.alpha = 1.0f
                binding.viewSubmittedIndicator.visibility = View.VISIBLE
                binding.viewSubmittedIndicator.setBackgroundColor(binding.root.context.getColor(com.example.watermeterapp.R.color.success_color))
                binding.tvSubMeterName.setTextColor(binding.root.context.getColor(android.R.color.black))
                binding.tvSubMeterName.text = subType.name
            } else {
                // 未提交状态
                binding.layoutSubMeter.alpha = 1.0f
                binding.viewSubmittedIndicator.visibility = View.GONE
                binding.tvSubMeterName.setTextColor(binding.root.context.getColor(android.R.color.black))
                binding.tvSubMeterName.text = subType.name
            }
        }
    }
    
    /**
     * 用于列表显示的数据项封装类
     */
    sealed class DisplayItem {
        data class ParentItem(val meterType: MonthlyMeterType) : DisplayItem()
        data class ChildItem(val parentType: String, val subType: MonthlyMeterType.SubMeterType) : DisplayItem()
    }
}