package com.example.watermeterapp.data.api.adapter

import com.example.watermeterapp.data.model.MonthlyMeterType
import com.google.gson.*
import java.lang.reflect.Type

/**
 * MonthlyMeterType类的自定义TypeAdapter，处理可能的类型转换问题
 */
class MonthlyMeterTypeAdapter : JsonDeserializer<MonthlyMeterType> {
    
    override fun deserialize(
        json: JsonElement, 
        typeOfT: Type, 
        context: JsonDeserializationContext
    ): MonthlyMeterType {
        val jsonObject = json.asJsonObject
        
        val parentType = jsonObject.get("parentType").asString
        var expanded = false
        
        // 安全地获取expanded字段，可能是布尔值或数字
        if (jsonObject.has("expanded")) {
            expanded = when (jsonObject.get("expanded").asJsonPrimitive.isBoolean) {
                true -> jsonObject.get("expanded").asBoolean
                false -> jsonObject.get("expanded").asInt != 0
            }
        }
        
        // 处理子类型列表
        val subTypes = mutableListOf<MonthlyMeterType.SubMeterType>()
        if (jsonObject.has("subTypes") && !jsonObject.get("subTypes").isJsonNull) {
            val subTypesArray = jsonObject.getAsJsonArray("subTypes")
            for (subTypeElement in subTypesArray) {
                val subTypeObj = subTypeElement.asJsonObject
                val name = subTypeObj.get("name").asString
                
                // 安全地获取hasNoMeter字段
                var hasNoMeter = false
                if (subTypeObj.has("hasNoMeter")) {
                    hasNoMeter = when (subTypeObj.get("hasNoMeter").asJsonPrimitive.isBoolean) {
                        true -> subTypeObj.get("hasNoMeter").asBoolean
                        false -> subTypeObj.get("hasNoMeter").asInt != 0
                    }
                }
                
                subTypes.add(MonthlyMeterType.SubMeterType(name, hasNoMeter))
            }
        }
        
        return MonthlyMeterType(parentType, subTypes, expanded)
    }
} 