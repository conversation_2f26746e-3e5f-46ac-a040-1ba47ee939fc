# 水表抄录系统 - 应用更新指南

## 不同包名更新机制

为了解决Android应用更新时需要先卸载再安装的问题，我们实现了一种使用不同包名的更新机制。这种机制允许新版本与旧版本共存，安装完成后再由新版本删除旧版本。

### 服务器端配置

服务器端已配置以下字段以支持不同包名更新：

- `packageName`: 新版本的应用包名（例如：com.watermeter.app.v2）
- `oldPackageName`: 旧版本的应用包名（例如：com.watermeter.app）
- `installMode`: 安装模式，设置为"AUTO_INSTALL"表示使用应用内更新

### Android客户端实现指南

#### 1. 构建配置

在Android项目的`build.gradle`文件中，使用不同的应用ID（包名）构建新版本：

```gradle
android {
    defaultConfig {
        // 使用不同的应用ID
        applicationId "com.watermeter.app.v2"  // 新版本包名
        // ...其他配置...
    }
}
```

#### 2. 更新检测与下载

检测到更新时，获取新版本的包名信息：

```java
public void checkForUpdates() {
    // 调用API检查更新
    apiService.checkForUpdates(currentVersion, new Callback<UpdateInfo>() {
        @Override
        public void onResponse(UpdateInfo updateInfo) {
            if (updateInfo != null) {
                // 获取新版本包名
                String newPackageName = updateInfo.getPackageName();
                String oldPackageName = updateInfo.getOldPackageName();
                
                // 下载并安装新版本
                downloadAndInstall(updateInfo.getDownloadUrl(), newPackageName);
            }
        }
    });
}
```

#### 3. 安装新版本

使用Android的PackageInstaller API安装下载好的APK：

```java
private void installNewVersion(File apkFile) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        Uri apkUri = FileProvider.getUriForFile(
            context,
            context.getPackageName() + ".provider",
            apkFile
        );
        
        Intent intent = new Intent(Intent.ACTION_INSTALL_PACKAGE);
        intent.setData(apkUri);
        intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
        intent.putExtra(Intent.EXTRA_NOT_UNKNOWN_SOURCE, true);
        intent.putExtra(Intent.EXTRA_INSTALLER_PACKAGE_NAME, context.getPackageName());
        context.startActivity(intent);
    } else {
        Uri apkUri = Uri.fromFile(apkFile);
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }
}
```

#### 4. 在新版本中删除旧版本

在新版本应用首次启动时，检查并卸载旧版本：

```java
public void checkAndUninstallOldVersion() {
    // 获取旧版本包名
    String oldPackageName = "com.watermeter.app";  // 从配置或本地存储获取
    
    // 检查旧版本是否已安装
    if (isPackageInstalled(oldPackageName)) {
        // 提示用户卸载旧版本
        showUninstallDialog(oldPackageName);
    }
}

private boolean isPackageInstalled(String packageName) {
    try {
        context.getPackageManager().getPackageInfo(packageName, 0);
        return true;
    } catch (PackageManager.NameNotFoundException e) {
        return false;
    }
}

private void showUninstallDialog(final String packageName) {
    new AlertDialog.Builder(context)
        .setTitle("卸载旧版本")
        .setMessage("检测到旧版本应用，是否卸载？")
        .setPositiveButton("卸载", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // 卸载旧版本
                Intent intent = new Intent(Intent.ACTION_DELETE);
                intent.setData(Uri.parse("package:" + packageName));
                context.startActivity(intent);
            }
        })
        .setNegativeButton("稍后", null)
        .show();
}
```

### 数据迁移

为了确保用户数据能够从旧版本迁移到新版本，可以采用以下策略：

#### 1. 共享存储

使用共享的外部存储或ContentProvider来存储应用数据，这样新旧版本可以访问相同的数据：

```java
public void migrateData() {
    File oldDataDir = new File(Environment.getExternalStorageDirectory(), "watermeter/data");
    if (oldDataDir.exists()) {
        // 读取旧版本数据
        // ...
    }
}
```

#### 2. 数据库迁移

如果使用SQLite数据库，可以将数据库文件放在共享位置：

```java
public class DatabaseHelper extends SQLiteOpenHelper {
    private static final String DB_NAME = "watermeter.db";
    private static final String EXTERNAL_DB_PATH = "/sdcard/watermeter/";
    
    public DatabaseHelper(Context context) {
        super(context, EXTERNAL_DB_PATH + DB_NAME, null, DB_VERSION);
    }
    
    // ...
}
```

#### 3. 云端同步

通过服务器同步数据，确保新版本可以从云端获取用户数据：

```java
public void syncDataFromServer() {
    apiService.getUserData(userId, new Callback<UserData>() {
        @Override
        public void onResponse(UserData userData) {
            // 保存从服务器获取的数据
            saveUserData(userData);
        }
    });
}
```

### 注意事项

1. **资源冲突**：确保新版本和旧版本可以共存，避免资源冲突
   - 使用不同的ContentProvider authority
   - 使用不同的FileProvider authority
   - 使用不同的SharedPreferences名称

2. **数据迁移**：确保用户数据能够从旧版本迁移到新版本
   - 考虑使用共享存储位置
   - 实现数据导入/导出功能
   - 利用云端同步

3. **用户体验**：提供良好的更新体验
   - 清晰地告知用户更新过程
   - 在新版本中提示用户卸载旧版本
   - 提供数据迁移的进度反馈

4. **权限处理**：确保新版本具有足够的权限来卸载旧版本
   - Android 11及以上版本需要特殊处理

5. **兼容性测试**：在不同Android版本上测试更新流程
   - 特别是Android 10、11等对应用安装有特殊限制的版本

通过这种方式，用户可以先安装新版本，然后再卸载旧版本，避免了更新过程中的问题。 