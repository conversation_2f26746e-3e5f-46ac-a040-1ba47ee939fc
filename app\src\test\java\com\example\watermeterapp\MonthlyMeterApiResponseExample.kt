package com.example.watermeterapp

import com.example.watermeterapp.data.model.MonthlyMeterType

/**
 * 示例文件：展示服务器月度抄表API的响应数据格式
 * 
 * 这个文件仅用于展示服务器返回的JSON格式和数据结构，不会实际发送请求
 */
object MonthlyMeterApiResponseExample {

    /**
     * 登录API响应示例
     * GET /api/auth/login?phone=13995944332
     */
    val loginResponseJson = """
    {
        "code": 0,
        "msg": "登录成功",
        "data": {
            "id": 22,
            "phoneNumber": "13995944332",
            "name": "邱东方",
            "department": "物业部",
            "role": "系统管理员",
            "isAuthorized": true,
            "authLevel": 1,
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MjIsInBob25lIjoiMTM5OTU5NDQzMzIiLCJuYW1lIjoi6YKx5Lic5pa5IiwiYXV0aExldmVsIjoxLCJpYXQiOjE3NTExMzA2NjQsImV4cCI6MTc1MTIxNzA2NH0.TMK_XfMmlyjdstJQ69vGN5MlK9BbRzMRBQMekeeITFI"
        }
    }
    """.trimIndent()

    /**
     * 月度抄表类型API响应示例
     * GET /api/meters/types/monthly-with-subtypes
     * 需要在请求头中包含Authorization: Bearer <token>
     */
    val monthlyMeterTypesResponseJson = """
    {
        "code": 0,
        "msg": "获取成功",
        "data": [
            {
                "parentType": "隔油间",
                "subTypes": [
                    {
                        "name": "1#隔油间",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#隔油间",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "3#隔油间",
                        "hasNoMeter": true,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false
            },
            {
                "parentType": "生活水泵房",
                "subTypes": [
                    {
                        "name": "1#水泵",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "2#水泵",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false
            },
            {
                "parentType": "中水机房",
                "subTypes": [
                    {
                        "name": "进水总表",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    },
                    {
                        "name": "出水总表",
                        "hasNoMeter": false,
                        "isSubmitted": false,
                        "isEnabled": true,
                        "isReactivated": false
                    }
                ],
                "expanded": false
            }
        ]
    }
    """.trimIndent()
    
    /**
     * 获取上一次读数API响应示例
     * GET /api/meters/last?meterType=中水机房/进水总表&isDaily=false
     */
    val lastReadingResponseJson = """
    {
        "id": 1542,
        "meterType": "中水机房/进水总表",
        "readingValue": 3456.8,
        "previousReading": 3432.5,
        "photoPath": null,
        "photoUrl": "https://storage.example.com/meters/photos/1542.jpg",
        "readingDate": "2025-05-28T14:23:45.000Z",
        "reader": "邱东方",
        "uploadTime": "2025-05-28T14:25:12.000Z",
        "isUploaded": true,
        "isDaily": false,
        "month": 5,
        "userPhone": "13995944332"
    }
    """.trimIndent()
    
    /**
     * 上传读数API请求示例
     * POST /api/meters/upload
     * 
     * 注意：这是一个multipart/form-data请求，包含两个部分：
     * 1. reading: JSON格式的读数数据
     * 2. photo: 照片文件
     */
    val uploadReadingRequestJson = """
    {
        "meterType": "中水机房/进水总表",
        "readingValue": 3478.2,
        "previousReading": 3456.8,
        "photoPath": "/storage/emulated/0/DCIM/Camera/IMG_20250628_103045.jpg",
        "readingDate": "2025-06-28T10:30:45.000Z",
        "isDaily": false,
        "month": 6,
        "userPhone": "13995944332"
    }
    """.trimIndent()
    
    /**
     * 上传读数API响应示例
     */
    val uploadReadingResponseJson = """
    {
        "code": 0,
        "msg": "上传成功",
        "data": {
            "id": 1643,
            "meterType": "中水机房/进水总表",
            "readingValue": 3478.2,
            "previousReading": 3456.8,
            "photoUrl": "https://storage.example.com/meters/photos/1643.jpg",
            "readingDate": "2025-06-28T10:30:45.000Z",
            "uploadTime": "2025-06-28T10:32:15.000Z",
            "isUploaded": true,
            "isDaily": false,
            "month": 6,
            "userPhone": "13995944332"
        }
    }
    """.trimIndent()
    
    /**
     * 获取示例月度抄表数据（与服务器返回格式一致）
     */
    fun getExampleMonthlyMeterData(): List<MonthlyMeterType> {
        return listOf(
            MonthlyMeterType("隔油间", listOf(
                MonthlyMeterType.SubMeterType("1#隔油间"),
                MonthlyMeterType.SubMeterType("2#隔油间"),
                MonthlyMeterType.SubMeterType("3#隔油间", hasNoMeter = true)
            )),
            MonthlyMeterType("生活水泵房", listOf(
                MonthlyMeterType.SubMeterType("1#水泵"),
                MonthlyMeterType.SubMeterType("2#水泵")
            )),
            MonthlyMeterType("中水机房", listOf(
                MonthlyMeterType.SubMeterType("进水总表"),
                MonthlyMeterType.SubMeterType("出水总表")
            ))
        )
    }
    
    /**
     * 打印月度抄表数据（格式化输出）
     */
    fun printMonthlyMeterData(): String {
        val meterTypes = getExampleMonthlyMeterData()
        val sb = StringBuilder()
        sb.appendLine("===== 月度抄表数据 =====")
        sb.appendLine("获取到${meterTypes.size}个月度抄表类型:")
        
        meterTypes.forEachIndexed { index, type ->
            sb.appendLine("${index + 1}. ${type.parentType} (${type.subTypes.size}个子类型)")
            type.subTypes.forEachIndexed { subIndex, subType ->
                val noMeterMark = if (subType.hasNoMeter) "[无表]" else ""
                sb.appendLine("   ${subIndex + 1}. ${subType.name} $noMeterMark")
            }
        }
        
        return sb.toString()
    }
    
    /**
     * 打印所有API响应数据
     */
    fun printAllApiResponses(): String {
        val sb = StringBuilder()
        
        // 1. 登录API响应
        sb.appendLine("===== 登录API响应 =====")
        sb.appendLine("请求: GET /api/auth/login?phone=13995944332")
        sb.appendLine("响应:")
        sb.appendLine(loginResponseJson)
        sb.appendLine()
        
        // 2. 月度抄表类型API响应
        sb.appendLine("===== 月度抄表类型API响应 =====")
        sb.appendLine("请求: GET /api/meters/types/monthly-with-subtypes")
        sb.appendLine("请求头: Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
        sb.appendLine("响应:")
        sb.appendLine(monthlyMeterTypesResponseJson)
        sb.appendLine()
        
        // 3. 格式化打印月度抄表数据
        sb.appendLine(printMonthlyMeterData())
        sb.appendLine()
        
        // 4. 获取上一次读数API响应
        sb.appendLine("===== 获取上一次读数API响应 =====")
        sb.appendLine("请求: GET /api/meters/last?meterType=中水机房/进水总表&isDaily=false")
        sb.appendLine("请求头: Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
        sb.appendLine("响应:")
        sb.appendLine(lastReadingResponseJson)
        sb.appendLine()
        
        // 5. 上传读数API响应
        sb.appendLine("===== 上传读数API响应 =====")
        sb.appendLine("请求: POST /api/meters/upload")
        sb.appendLine("请求头: Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
        sb.appendLine("请求体: multipart/form-data")
        sb.appendLine("Part 1 (reading): ")
        sb.appendLine(uploadReadingRequestJson)
        sb.appendLine("Part 2 (photo): [二进制图片数据]")
        sb.appendLine("响应:")
        sb.appendLine(uploadReadingResponseJson)
        
        return sb.toString()
    }
}

/**
 * 主函数，用于直接运行并打印所有API响应数据
 */
fun main() {
    println("开始模拟访问服务器月度抄表数据...")
    println("服务器地址: http://**************:3002/api/")
    println("用户手机号: 13995944332")
    println()
    
    // 打印所有API响应数据
    println(MonthlyMeterApiResponseExample.printAllApiResponses())
    
    println("模拟访问完成")
} 